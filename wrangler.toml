# Cloudflare Pages 配置文件
name = "podcast-website"
compatibility_date = "2024-01-15"

[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.upload]
format = "directory"
dir = "dist"

# 环境变量（在 Cloudflare Dashboard 中设置）
[env.production.vars]
# CLOUDFLARE_ACCOUNT_ID = "your_account_id"
# CLOUDFLARE_R2_ACCESS_KEY_ID = "your_access_key_id"
# CLOUDFLARE_R2_SECRET_ACCESS_KEY = "your_secret_access_key"
# CLOUDFLARE_R2_BUCKET_NAME = "podcast-audio"
# CLOUDFLARE_R2_PUBLIC_URL = "https://audio.yourpodcast.com"
