export * as z from 'zod';
export { defineLiveCollection } from '../content/config.js';
export declare const getCollection: () => never;
export declare const render: () => never;
export declare const getEntry: () => never;
export declare const getEntryBySlug: () => never;
export declare const getDataEntryById: () => never;
export declare const getEntries: () => never;
export declare const reference: () => never;
export declare const getLiveCollection: () => never;
export declare const getLiveEntry: () => never;
export declare const defineCollection: () => never;
