const lang = Object.freeze(JSON.parse("{\"displayName\":\"Puppet\",\"fileTypes\":[\"pp\"],\"foldingStartMarker\":\"(^\\\\s*/\\\\*|([(\\\\[{])\\\\s*$)\",\"foldingStopMarker\":\"(\\\\*/|^\\\\s*([])}]))\",\"name\":\"puppet\",\"patterns\":[{\"include\":\"#line_comment\"},{\"include\":\"#constants\"},{\"begin\":\"^\\\\s*/\\\\*\",\"end\":\"\\\\*/\",\"name\":\"comment.block.puppet\"},{\"begin\":\"\\\\b(node)\\\\b\",\"captures\":{\"1\":{\"name\":\"storage.type.puppet\"},\"2\":{\"name\":\"entity.name.type.class.puppet\"}},\"end\":\"(?=\\\\{)\",\"name\":\"meta.definition.class.puppet\",\"patterns\":[{\"match\":\"\\\\bdefault\\\\b\",\"name\":\"keyword.puppet\"},{\"include\":\"#strings\"},{\"include\":\"#regex-literal\"}]},{\"begin\":\"\\\\b(class)\\\\s+((?:[a-z][0-9_a-z]*)?(?:::[a-z][0-9_a-z]*)+|[a-z][0-9_a-z]*)\\\\s*\",\"captures\":{\"1\":{\"name\":\"storage.type.puppet\"},\"2\":{\"name\":\"entity.name.type.class.puppet\"}},\"end\":\"(?=\\\\{)\",\"name\":\"meta.definition.class.puppet\",\"patterns\":[{\"begin\":\"\\\\b(inherits)\\\\b\\\\s+\",\"captures\":{\"1\":{\"name\":\"storage.modifier.puppet\"}},\"end\":\"(?=[({])\",\"name\":\"meta.definition.class.inherits.puppet\",\"patterns\":[{\"match\":\"\\\\b((?:[-\\\".0-9A-Z_a-z]+::)*[-\\\".0-9A-Z_a-z]+)\\\\b\",\"name\":\"support.type.puppet\"}]},{\"include\":\"#line_comment\"},{\"include\":\"#resource-parameters\"},{\"include\":\"#parameter-default-types\"}]},{\"begin\":\"^\\\\s*(plan)\\\\s+((?:[a-z][0-9_a-z]*)?(?:::[a-z][0-9_a-z]*)+|[a-z][0-9_a-z]*)\\\\s*\",\"captures\":{\"1\":{\"name\":\"storage.type.puppet\"},\"2\":{\"name\":\"entity.name.type.plan.puppet\"}},\"end\":\"(?=\\\\{)\",\"name\":\"meta.definition.plan.puppet\",\"patterns\":[{\"include\":\"#line_comment\"},{\"include\":\"#resource-parameters\"},{\"include\":\"#parameter-default-types\"}]},{\"begin\":\"^\\\\s*(define|function)\\\\s+([a-z][0-9_a-z]*|(?:[a-z][0-9_a-z]*)?(?:::[a-z][0-9_a-z]*)+)\\\\s*(\\\\()\",\"captures\":{\"1\":{\"name\":\"storage.type.function.puppet\"},\"2\":{\"name\":\"entity.name.function.puppet\"}},\"end\":\"(?=\\\\{)\",\"name\":\"meta.function.puppet\",\"patterns\":[{\"include\":\"#line_comment\"},{\"include\":\"#resource-parameters\"},{\"include\":\"#parameter-default-types\"}]},{\"captures\":{\"1\":{\"name\":\"keyword.control.puppet\"}},\"match\":\"\\\\b(case|else|elsif|if|unless)(?!::)\\\\b\"},{\"include\":\"#keywords\"},{\"include\":\"#resource-definition\"},{\"include\":\"#heredoc\"},{\"include\":\"#strings\"},{\"include\":\"#puppet-datatypes\"},{\"include\":\"#array\"},{\"match\":\"((\\\\$?)\\\"?[A-Z_a-z\\\\x7F-ÿ][0-9A-Z_a-z\\\\x7F-ÿ]*\\\"?):(?=\\\\s+|$)\",\"name\":\"entity.name.section.puppet\"},{\"include\":\"#numbers\"},{\"include\":\"#variable\"},{\"begin\":\"\\\\b(import|include|contain|require)\\\\s+(?!.*=>)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.import.include.puppet\"}},\"contentName\":\"variable.parameter.include.puppet\",\"end\":\"(?=\\\\s|$)\",\"name\":\"meta.include.puppet\"},{\"match\":\"\\\\b\\\\w+\\\\s*(?==>)\\\\s*\",\"name\":\"constant.other.key.puppet\"},{\"match\":\"(?<=\\\\{)\\\\s*\\\\w+\\\\s*(?=})\",\"name\":\"constant.other.bareword.puppet\"},{\"match\":\"\\\\b(alert|crit|debug|defined|emerg|err|escape|fail|failed|file|generate|gsub|info|notice|package|realize|search|tag|tagged|template|warning)\\\\b(?!.*\\\\{)\",\"name\":\"support.function.puppet\"},{\"match\":\"=>\",\"name\":\"punctuation.separator.key-value.puppet\"},{\"match\":\"->\",\"name\":\"keyword.control.orderarrow.puppet\"},{\"match\":\"~>\",\"name\":\"keyword.control.notifyarrow.puppet\"},{\"include\":\"#regex-literal\"}],\"repository\":{\"array\":{\"begin\":\"(\\\\[)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.array.begin.puppet\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.array.end.puppet\"}},\"name\":\"meta.array.puppet\",\"patterns\":[{\"match\":\"\\\\s*,\\\\s*\"},{\"include\":\"#parameter-default-types\"},{\"include\":\"#line_comment\"}]},\"constants\":{\"patterns\":[{\"match\":\"\\\\b(absent|directory|false|file|present|running|stopped|true)\\\\b(?!.*\\\\{)\",\"name\":\"constant.language.puppet\"}]},\"double-quoted-string\":{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.puppet\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.puppet\"}},\"name\":\"string.quoted.double.interpolated.puppet\",\"patterns\":[{\"include\":\"#escaped_char\"},{\"include\":\"#interpolated_puppet\"}]},\"escaped_char\":{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.puppet\"},\"function_call\":{\"begin\":\"([A-Z_a-z][0-9A-Z_a-z]*)(\\\\()\",\"end\":\"\\\\)\",\"name\":\"meta.function-call.puppet\",\"patterns\":[{\"include\":\"#parameter-default-types\"},{\"match\":\",\",\"name\":\"punctuation.separator.parameters.puppet\"}]},\"hash\":{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.hash.begin.puppet\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.hash.end.puppet\"}},\"name\":\"meta.hash.puppet\",\"patterns\":[{\"match\":\"\\\\b\\\\w+\\\\s*(?==>)\\\\s*\",\"name\":\"constant.other.key.puppet\"},{\"include\":\"#parameter-default-types\"},{\"include\":\"#line_comment\"}]},\"heredoc\":{\"patterns\":[{\"begin\":\"@\\\\(\\\\p{blank}*\\\"([^\\\\t )/:]+)\\\"\\\\p{blank}*(:\\\\p{blank}*[a-z][+0-9A-Z_a-z]*\\\\p{blank}*)?(/\\\\p{blank}*[$Lnrst]*)?\\\\p{blank}*\\\\)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.puppet\"}},\"end\":\"^\\\\p{blank}*(\\\\|\\\\p{blank}*-|[-|])?\\\\p{blank}*\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.puppet\"}},\"name\":\"string.interpolated.heredoc.puppet\",\"patterns\":[{\"include\":\"#escaped_char\"},{\"include\":\"#interpolated_puppet\"}]},{\"begin\":\"@\\\\(\\\\p{blank}*([^\\\\t )/:]+)\\\\p{blank}*(:\\\\p{blank}*[a-z][+0-9A-Z_a-z]*\\\\p{blank}*)?(/\\\\p{blank}*[$Lnrst]*)?\\\\p{blank}*\\\\)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.puppet\"}},\"end\":\"^\\\\p{blank}*(\\\\|\\\\p{blank}*-|[-|])?\\\\p{blank}*\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.puppet\"}},\"name\":\"string.unquoted.heredoc.puppet\"}]},\"interpolated_puppet\":{\"patterns\":[{\"begin\":\"(\\\\$\\\\{)(\\\\d+)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.section.embedded.begin.puppet\"},\"2\":{\"name\":\"source.puppet variable.other.readwrite.global.pre-defined.puppet\"}},\"contentName\":\"source.puppet\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.end.puppet\"}},\"name\":\"meta.embedded.line.puppet\",\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"(\\\\$\\\\{)(_[0-9A-Z_a-z]*)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.section.embedded.begin.puppet\"},\"2\":{\"name\":\"source.puppet variable.other.readwrite.global.puppet\"}},\"contentName\":\"source.puppet\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.end.puppet\"}},\"name\":\"meta.embedded.line.puppet\",\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"(\\\\$\\\\{)(([a-z][0-9_a-z]*)?(?:::[a-z][0-9_a-z]*)*)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.section.embedded.begin.puppet\"},\"2\":{\"name\":\"source.puppet variable.other.readwrite.global.puppet\"}},\"contentName\":\"source.puppet\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.end.puppet\"}},\"name\":\"meta.embedded.line.puppet\",\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"\\\\$\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.begin.puppet\"}},\"contentName\":\"source.puppet\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.end.puppet\"}},\"name\":\"meta.embedded.line.puppet\",\"patterns\":[{\"include\":\"$self\"}]}]},\"keywords\":{\"captures\":{\"1\":{\"name\":\"keyword.puppet\"}},\"match\":\"\\\\b(undef)\\\\b\"},\"line_comment\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"comment.line.number-sign.puppet\"},\"2\":{\"name\":\"punctuation.definition.comment.puppet\"}},\"match\":\"^((#).*$\\\\n?)\",\"name\":\"meta.comment.full-line.puppet\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.comment.puppet\"}},\"match\":\"(#).*$\\\\n?\",\"name\":\"comment.line.number-sign.puppet\"}]},\"nested_braces\":{\"begin\":\"\\\\{\",\"captures\":{\"1\":{\"name\":\"punctuation.section.scope.puppet\"}},\"end\":\"}\",\"patterns\":[{\"include\":\"#escaped_char\"},{\"include\":\"#nested_braces\"}]},\"nested_braces_interpolated\":{\"begin\":\"\\\\{\",\"captures\":{\"1\":{\"name\":\"punctuation.section.scope.puppet\"}},\"end\":\"}\",\"patterns\":[{\"include\":\"#escaped_char\"},{\"include\":\"#variable\"},{\"include\":\"#nested_braces_interpolated\"}]},\"nested_brackets\":{\"begin\":\"\\\\[\",\"captures\":{\"1\":{\"name\":\"punctuation.section.scope.puppet\"}},\"end\":\"]\",\"patterns\":[{\"include\":\"#escaped_char\"},{\"include\":\"#nested_brackets\"}]},\"nested_brackets_interpolated\":{\"begin\":\"\\\\[\",\"captures\":{\"1\":{\"name\":\"punctuation.section.scope.puppet\"}},\"end\":\"]\",\"patterns\":[{\"include\":\"#escaped_char\"},{\"include\":\"#variable\"},{\"include\":\"#nested_brackets_interpolated\"}]},\"nested_parens\":{\"begin\":\"\\\\(\",\"captures\":{\"1\":{\"name\":\"punctuation.section.scope.puppet\"}},\"end\":\"\\\\)\",\"patterns\":[{\"include\":\"#escaped_char\"},{\"include\":\"#nested_parens\"}]},\"nested_parens_interpolated\":{\"begin\":\"\\\\(\",\"captures\":{\"1\":{\"name\":\"punctuation.section.scope.puppet\"}},\"end\":\"\\\\)\",\"patterns\":[{\"include\":\"#escaped_char\"},{\"include\":\"#variable\"},{\"include\":\"#nested_parens_interpolated\"}]},\"numbers\":{\"patterns\":[{\"match\":\"(?<![\\\\w\\\\d])([-+]?)(?i:0x)(?i:[0-9a-f])+(?![\\\\w\\\\d])\",\"name\":\"constant.numeric.hexadecimal.puppet\"},{\"match\":\"(?<![.\\\\w])([-+]?)(?<!\\\\d)\\\\d+(?i:e([-+])?\\\\d+)?(?![.\\\\w\\\\d])\",\"name\":\"constant.numeric.integer.puppet\"},{\"match\":\"(?<!\\\\w)([-+]?)\\\\d+\\\\.\\\\d+(?i:e([-+])?\\\\d+)?(?![\\\\w\\\\d])\",\"name\":\"constant.numeric.integer.puppet\"}]},\"parameter-default-types\":{\"patterns\":[{\"include\":\"#strings\"},{\"include\":\"#numbers\"},{\"include\":\"#variable\"},{\"include\":\"#hash\"},{\"include\":\"#array\"},{\"include\":\"#function_call\"},{\"include\":\"#constants\"},{\"include\":\"#puppet-datatypes\"}]},\"puppet-datatypes\":{\"patterns\":[{\"match\":\"(?<![$A-Za-z])([A-Z][0-9A-Z_a-z]*)(?![0-9A-Z_a-z])\",\"name\":\"storage.type.puppet\"}]},\"regex-literal\":{\"match\":\"(/)(.+?)[^\\\\\\\\]/\",\"name\":\"string.regexp.literal.puppet\"},\"resource-definition\":{\"begin\":\"(?:^|\\\\b)(::[a-z][0-9_a-z]*|[a-z][0-9_a-z]*|(?:[a-z][0-9_a-z]*)?(?:::[a-z][0-9_a-z]*)+)\\\\s*(\\\\{)\\\\s*\",\"beginCaptures\":{\"1\":{\"name\":\"meta.definition.resource.puppet storage.type.puppet\"}},\"contentName\":\"entity.name.section.puppet\",\"end\":\":\",\"patterns\":[{\"include\":\"#strings\"},{\"include\":\"#variable\"},{\"include\":\"#array\"}]},\"resource-parameters\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"variable.other.puppet\"},\"2\":{\"name\":\"punctuation.definition.variable.puppet\"}},\"match\":\"((\\\\$+)[A-Z_a-z][0-9A-Z_a-z]*)\\\\s*(?=[),])\",\"name\":\"meta.function.argument.puppet\"},{\"begin\":\"((\\\\$+)[A-Z_a-z][0-9A-Z_a-z]*)\\\\s*(=)\\\\s*\\\\s*\",\"captures\":{\"1\":{\"name\":\"variable.other.puppet\"},\"2\":{\"name\":\"punctuation.definition.variable.puppet\"},\"3\":{\"name\":\"keyword.operator.assignment.puppet\"}},\"end\":\"(?=[),])\",\"name\":\"meta.function.argument.puppet\",\"patterns\":[{\"include\":\"#parameter-default-types\"}]}]},\"single-quoted-string\":{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.puppet\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.puppet\"}},\"name\":\"string.quoted.single.puppet\",\"patterns\":[{\"include\":\"#escaped_char\"}]},\"strings\":{\"patterns\":[{\"include\":\"#double-quoted-string\"},{\"include\":\"#single-quoted-string\"}]},\"variable\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.puppet\"}},\"match\":\"(\\\\$)(\\\\d+)\",\"name\":\"variable.other.readwrite.global.pre-defined.puppet\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.puppet\"}},\"match\":\"(\\\\$)_[0-9A-Z_a-z]*\",\"name\":\"variable.other.readwrite.global.puppet\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.puppet\"}},\"match\":\"(\\\\$)(([a-z][0-9A-Z_a-z]*)?(?:::[a-z][0-9A-Z_a-z]*)*)\",\"name\":\"variable.other.readwrite.global.puppet\"}]}},\"scopeName\":\"source.puppet\"}"))

export default [
lang
]
