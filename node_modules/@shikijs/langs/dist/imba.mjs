const lang = Object.freeze(JSON.parse("{\"displayName\":\"Imba\",\"fileTypes\":[\"imba\",\"imba2\"],\"name\":\"imba\",\"patterns\":[{\"include\":\"#root\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.comment.imba\"}},\"match\":\"\\\\A(#!).*(?=$)\",\"name\":\"comment.line.shebang.imba\"}],\"repository\":{\"array-literal\":{\"begin\":\"\\\\s*(\\\\[)\",\"beginCaptures\":{\"1\":{\"name\":\"meta.brace.square.imba\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"meta.brace.square.imba\"}},\"name\":\"meta.array.literal.imba\",\"patterns\":[{\"include\":\"#expr\"},{\"include\":\"#punctuation-comma\"}]},\"block\":{\"patterns\":[{\"include\":\"#style-declaration\"},{\"include\":\"#mixin-declaration\"},{\"include\":\"#object-keys\"},{\"include\":\"#generics-literal\"},{\"include\":\"#tag-literal\"},{\"include\":\"#regex\"},{\"include\":\"#keywords\"},{\"include\":\"#comment\"},{\"include\":\"#literal\"},{\"include\":\"#plain-identifiers\"},{\"include\":\"#plain-accessors\"},{\"include\":\"#pairs\"},{\"include\":\"#invalid-indentation\"}]},\"boolean-literal\":{\"patterns\":[{\"match\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(true|yes)(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"constant.language.boolean.true.imba\"},{\"match\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(false|no)(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"constant.language.boolean.false.imba\"}]},\"brackets\":{\"patterns\":[{\"begin\":\"\\\\{\",\"end\":\"}|(?=\\\\*/)\",\"patterns\":[{\"include\":\"#brackets\"}]},{\"begin\":\"\\\\[\",\"end\":\"]|(?=\\\\*/)\",\"patterns\":[{\"include\":\"#brackets\"}]}]},\"comment\":{\"patterns\":[{\"begin\":\"/\\\\*\\\\*(?!/)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.imba\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.imba\"}},\"name\":\"comment.block.documentation.imba\",\"patterns\":[{\"include\":\"#docblock\"}]},{\"begin\":\"(/\\\\*)(?:\\\\s*((@)internal)(?=\\\\s|(\\\\*/)))?\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.imba\"},\"2\":{\"name\":\"storage.type.internaldeclaration.imba\"},\"3\":{\"name\":\"punctuation.decorator.internaldeclaration.imba\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.imba\"}},\"name\":\"comment.block.imba\"},{\"begin\":\"(### @ts(?=\\\\s|$))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.imba\"}},\"contentName\":\"source.ts.embedded.imba\",\"end\":\"###\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.imba\"}},\"name\":\"ts.block.imba\"},{\"begin\":\"(###)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.imba\"}},\"end\":\"###[\\\\t ]*\\\\n\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.imba\"}},\"name\":\"comment.block.imba\"},{\"begin\":\"(^[\\\\t ]+)?((//|#\\\\s)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.imba\"},\"2\":{\"name\":\"comment.line.double-slash.imba\"},\"3\":{\"name\":\"punctuation.definition.comment.imba\"},\"4\":{\"name\":\"storage.type.internaldeclaration.imba\"},\"5\":{\"name\":\"punctuation.decorator.internaldeclaration.imba\"}},\"contentName\":\"comment.line.double-slash.imba\",\"end\":\"(?=$)\"}]},\"css-color-keywords\":{\"patterns\":[{\"match\":\"(?i)(?<![-\\\\w])(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)(?![-\\\\w])\",\"name\":\"support.constant.color.w3c-standard-color-name.css\"},{\"match\":\"(?i)(?<![-\\\\w])(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|rebeccapurple|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato|transparent|turquoise|violet|wheat|whitesmoke|yellowgreen)(?![-\\\\w])\",\"name\":\"support.constant.color.w3c-extended-color-name.css\"},{\"match\":\"(?i)(?<![-\\\\w])currentColor(?![-\\\\w])\",\"name\":\"support.constant.color.current.css\"}]},\"css-combinators\":{\"patterns\":[{\"match\":\">>>?|[+>~]\",\"name\":\"punctuation.separator.combinator.css\"},{\"match\":\"&\",\"name\":\"keyword.other.parent-selector.css\"}]},\"css-commas\":{\"match\":\",\",\"name\":\"punctuation.separator.list.comma.css\"},\"css-comment\":{\"patterns\":[{\"match\":\"#(\\\\s.+)?(\\\\n|$)\",\"name\":\"comment.line.imba\"},{\"match\":\"^(\\\\t+)(#(\\\\s.+)?(\\\\n|$))\",\"name\":\"comment.line.imba\"}]},\"css-escapes\":{\"patterns\":[{\"match\":\"\\\\\\\\\\\\h{1,6}\",\"name\":\"constant.character.escape.codepoint.css\"},{\"begin\":\"\\\\\\\\$\\\\s*\",\"end\":\"^(?<!\\\\G)\",\"name\":\"constant.character.escape.newline.css\"},{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.css\"}]},\"css-functions\":{\"patterns\":[{\"begin\":\"(?i)(?<![-\\\\w])(calc)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.calc.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.calc.css\",\"patterns\":[{\"match\":\"[*/]|(?<=\\\\s|^)[-+](?=\\\\s|$)\",\"name\":\"keyword.operator.arithmetic.css\"},{\"include\":\"#css-property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(rgba?|hsla?)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.misc.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.color.css\",\"patterns\":[{\"include\":\"#css-property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])((?:-(?:webkit-|moz-|o-))?(?:repeating-)?(?:linear|radial|conic)-gradient)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.gradient.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.gradient.css\",\"patterns\":[{\"match\":\"(?i)(?<![-\\\\w])(from|to|at)(?![-\\\\w])\",\"name\":\"keyword.operator.gradient.css\"},{\"include\":\"#css-property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(-webkit-gradient)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"invalid.deprecated.gradient.function.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.gradient.invalid.deprecated.gradient.css\",\"patterns\":[{\"begin\":\"(?i)(?<![-\\\\w])(from|to|color-stop)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"invalid.deprecated.function.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"patterns\":[{\"include\":\"#css-property-values\"}]},{\"include\":\"#css-property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(annotation|attr|blur|brightness|character-variant|contrast|counters?|cross-fade|drop-shadow|element|fit-content|format|grayscale|hue-rotate|image-set|invert|local|minmax|opacity|ornaments|repeat|saturate|sepia|styleset|stylistic|swash|symbols)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.misc.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.misc.css\",\"patterns\":[{\"match\":\"(?i)(?<=[\\\",\\\\s]|\\\\*/|^)\\\\d+x(?=[\\\"'),\\\\s]|/\\\\*|$)\",\"name\":\"constant.numeric.other.density.css\"},{\"include\":\"#css-property-values\"},{\"match\":\"[^\\\"'),\\\\s]+\",\"name\":\"variable.parameter.misc.css\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(circle|ellipse|inset|polygon|rect)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.shape.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.shape.css\",\"patterns\":[{\"match\":\"(?i)(?<=\\\\s|^|\\\\*/)(at|round)(?=\\\\s|/\\\\*|$)\",\"name\":\"keyword.operator.shape.css\"},{\"include\":\"#css-property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(cubic-bezier|steps)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.timing-function.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.timing-function.css\",\"patterns\":[{\"match\":\"(?i)(?<![-\\\\w])(start|end)(?=\\\\s*\\\\)|$)\",\"name\":\"support.constant.step-direction.css\"},{\"include\":\"#css-property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])((?:translate|scale|rotate)(?:[XYZ]|3D)?|matrix(?:3D)?|skew[XY]?|perspective)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.transform.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"patterns\":[{\"include\":\"#css-property-values\"}]}]},\"css-numeric-values\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.css\"}},\"match\":\"(#)(?:\\\\h{3,4}|\\\\h{6}|\\\\h{8})\\\\b\",\"name\":\"constant.other.color.rgb-value.hex.css\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.unit.percentage.css\"},\"2\":{\"name\":\"keyword.other.unit.${2:/downcase}.css\"}},\"match\":\"(?i)(?<![-\\\\w])[-+]?(?:[0-9]+(?:\\\\.[0-9]+)?|\\\\.[0-9]+)(?:(?<=[0-9])E[-+]?[0-9]+)?(?:(%)|(deg|grad|rad|turn|Hz|kHz|ch|cm|em|ex|fr|in|mm|mozmm|pc|pt|px|q|rem|vh|vmax|vmin|vw|dpi|dpcm|dppx|s|ms)\\\\b)?\",\"name\":\"constant.numeric.css\"}]},\"css-property-values\":{\"patterns\":[{\"include\":\"#css-commas\"},{\"include\":\"#css-escapes\"},{\"include\":\"#css-functions\"},{\"include\":\"#css-numeric-values\"},{\"include\":\"#css-size-keywords\"},{\"include\":\"#css-color-keywords\"},{\"include\":\"#string\"},{\"match\":\"!\\\\s*important(?![-\\\\w])\",\"name\":\"keyword.other.important.css\"}]},\"css-pseudo-classes\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.css\"},\"2\":{\"name\":\"invalid.illegal.colon.css\"}},\"match\":\"(?i)(:)(:*)(?:active|any-link|checked|default|defined|disabled|empty|enabled|first|(?:first|last|only)-(?:child|of-type)|focus|focus-visible|focus-within|fullscreen|host|hover|in-range|indeterminate|invalid|left|link|optional|out-of-range|placeholder-shown|read-only|read-write|required|right|root|scope|target|unresolved|valid|visited)(?![-\\\\w]|\\\\s*[;}])\",\"name\":\"entity.other.attribute-name.pseudo-class.css\"},\"css-pseudo-elements\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.css\"},\"2\":{\"name\":\"punctuation.definition.entity.css\"}},\"match\":\"(?i)(?:(::?)(?:after|before|first-letter|first-line|(?:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[-a-z]+)|(::)(?:backdrop|content|grammar-error|marker|placeholder|selection|shadow|spelling-error))(?![-\\\\w]|\\\\s*[;}])\",\"name\":\"entity.other.attribute-name.pseudo-element.css\"},\"css-selector\":{\"begin\":\"(?<=css\\\\s)(?![-!$%.@^\\\\w]+\\\\s*[:=][^:])\",\"end\":\"(\\\\s*(?=[-!$%.@^\\\\w]+\\\\s*[:=][^:])|\\\\s*$|(?=\\\\s+#\\\\s))\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.separator.sel-properties.css\"}},\"name\":\"meta.selector.css\",\"patterns\":[{\"include\":\"#css-selector-innards\"}]},\"css-selector-innards\":{\"patterns\":[{\"include\":\"#css-commas\"},{\"include\":\"#css-escapes\"},{\"include\":\"#css-combinators\"},{\"match\":\"(%[-\\\\w]+)\",\"name\":\"entity.other.attribute-name.mixin.css\"},{\"match\":\"\\\\*\",\"name\":\"entity.name.tag.wildcard.css\"},{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.entity.begin.bracket.square.css\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.entity.end.bracket.square.css\"}},\"name\":\"meta.attribute-selector.css\",\"patterns\":[{\"include\":\"#string\"},{\"captures\":{\"1\":{\"name\":\"storage.modifier.ignore-case.css\"}},\"match\":\"(?<=[\\\"'\\\\s]|^|\\\\*/)\\\\s*([Ii])\\\\s*(?=[]\\\\s]|/\\\\*|$)\"},{\"captures\":{\"1\":{\"name\":\"string.unquoted.attribute-value.css\"}},\"match\":\"(?<==)\\\\s*((?!/\\\\*)(?:[^]\\\"'\\\\\\\\\\\\s]|\\\\\\\\.)+)\"},{\"include\":\"#css-escapes\"},{\"match\":\"[$*^|~]?=\",\"name\":\"keyword.operator.pattern.css\"},{\"match\":\"\\\\|\",\"name\":\"punctuation.separator.css\"},{\"captures\":{\"1\":{\"name\":\"entity.other.namespace-prefix.css\"}},\"match\":\"(-?(?!\\\\d)(?:[-\\\\w[^0-\\\\\\\\x]]|\\\\\\\\(?:\\\\h{1,6}|.))+|\\\\*)(?=\\\\|(?![=\\\\s]|$|])(?:-?(?!\\\\d)|[-\\\\\\\\\\\\w[^0-\\\\\\\\x]]))\"},{\"captures\":{\"1\":{\"name\":\"entity.other.attribute-name.css\"}},\"match\":\"(-?(?!\\\\d)(?>[-\\\\w[^0-\\\\\\\\x]]|\\\\\\\\(?:\\\\h{1,6}|.))+)\\\\s*(?=[]$*=^|~]|/\\\\*)\"}]},{\"include\":\"#css-pseudo-classes\"},{\"include\":\"#css-pseudo-elements\"},{\"include\":\"#css-mixin\"}]},\"css-size-keywords\":{\"patterns\":[{\"match\":\"(x+s|sm-|md-|lg-|sm|md|lg|x+l|hg|x+h)(?![-\\\\w])\",\"name\":\"support.constant.size.property-value.css\"}]},\"curly-braces\":{\"begin\":\"\\\\s*(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"meta.brace.curly.imba\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"meta.brace.curly.imba\"}},\"patterns\":[{\"include\":\"#expr\"},{\"include\":\"#punctuation-comma\"}]},\"decorator\":{\"begin\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))@(?!@)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.decorator.imba\"}},\"end\":\"(?=\\\\s)\",\"name\":\"meta.decorator.imba\",\"patterns\":[{\"include\":\"#expr\"}]},\"directives\":{\"begin\":\"^(///)\\\\s*(?=<(reference|amd-dependency|amd-module)(\\\\s+(path|types|no-default-lib|lib|name)\\\\s*=\\\\s*(('([^'\\\\\\\\]|\\\\\\\\.)*')|(\\\"([^\\\"\\\\\\\\]|\\\\\\\\.)*\\\")|(`([^\\\\\\\\`]|\\\\\\\\.)*`)))+\\\\s*/>\\\\s*$)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.imba\"}},\"end\":\"(?=$)\",\"name\":\"comment.line.triple-slash.directive.imba\",\"patterns\":[{\"begin\":\"(<)(reference|amd-dependency|amd-module)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.directive.imba\"},\"2\":{\"name\":\"entity.name.tag.directive.imba\"}},\"end\":\"/>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.directive.imba\"}},\"name\":\"meta.tag.imba\",\"patterns\":[{\"match\":\"path|types|no-default-lib|lib|name\",\"name\":\"entity.other.attribute-name.directive.imba\"},{\"match\":\"=\",\"name\":\"keyword.operator.assignment.imba\"},{\"include\":\"#string\"}]}]},\"docblock\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"constant.language.access-type.jsdoc\"}},\"match\":\"((@)a(?:ccess|pi))\\\\s+(p(?:rivate|rotected|ublic))\\\\b\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"entity.name.type.instance.jsdoc\"},\"4\":{\"name\":\"punctuation.definition.bracket.angle.begin.jsdoc\"},\"5\":{\"name\":\"constant.other.email.link.underline.jsdoc\"},\"6\":{\"name\":\"punctuation.definition.bracket.angle.end.jsdoc\"}},\"match\":\"((@)author)\\\\s+([^*/<>@\\\\s](?:[^*/<>@]|\\\\*[^/])*)(?:\\\\s*(<)([^>\\\\s]+)(>))?\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"entity.name.type.instance.jsdoc\"},\"4\":{\"name\":\"keyword.operator.control.jsdoc\"},\"5\":{\"name\":\"entity.name.type.instance.jsdoc\"}},\"match\":\"((@)borrows)\\\\s+((?:[^*/@\\\\s]|\\\\*[^/])+)\\\\s+(as)\\\\s+((?:[^*/@\\\\s]|\\\\*[^/])+)\"},{\"begin\":\"((@)example)\\\\s+\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"}},\"end\":\"(?=@|\\\\*/)\",\"name\":\"meta.example.jsdoc\",\"patterns\":[{\"match\":\"^\\\\s\\\\*\\\\s+\"},{\"begin\":\"\\\\G(<)caption(>)\",\"beginCaptures\":{\"0\":{\"name\":\"entity.name.tag.inline.jsdoc\"},\"1\":{\"name\":\"punctuation.definition.bracket.angle.begin.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.bracket.angle.end.jsdoc\"}},\"contentName\":\"constant.other.description.jsdoc\",\"end\":\"(</)caption(>)|(?=\\\\*/)\",\"endCaptures\":{\"0\":{\"name\":\"entity.name.tag.inline.jsdoc\"},\"1\":{\"name\":\"punctuation.definition.bracket.angle.begin.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.bracket.angle.end.jsdoc\"}}},{\"captures\":{\"0\":{\"name\":\"source.embedded.imba\"}},\"match\":\"[^*@\\\\s](?:[^*]|\\\\*[^/])*\"}]},{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"constant.language.symbol-type.jsdoc\"}},\"match\":\"((@)kind)\\\\s+(class|constant|event|external|file|function|member|mixin|module|namespace|typedef)\\\\b\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"variable.other.link.underline.jsdoc\"},\"4\":{\"name\":\"entity.name.type.instance.jsdoc\"}},\"match\":\"((@)see)\\\\s+(?:((?=https?://)(?:[^*\\\\s]|\\\\*[^/])+)|((?!https?://|(?:\\\\[[^]\\\\[]*])?\\\\{@(?:link|linkcode|linkplain|tutorial)\\\\b)(?:[^*/@\\\\s]|\\\\*[^/])+))\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"variable.other.jsdoc\"}},\"match\":\"((@)template)\\\\s+([$A-Z_a-z][]$.\\\\[\\\\w]*(?:\\\\s*,\\\\s*[$A-Z_a-z][]$.\\\\[\\\\w]*)*)\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"variable.other.jsdoc\"}},\"match\":\"((@)(?:arg|argument|const|constant|member|namespace|param|var))\\\\s+([$A-Z_a-z][]$.\\\\[\\\\w]*)\"},{\"begin\":\"((@)typedef)\\\\s+(?=\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"}},\"end\":\"(?=\\\\s|\\\\*/|[^]$A-\\\\[_a-{}])\",\"patterns\":[{\"include\":\"#jsdoctype\"},{\"match\":\"(?:[^*/@\\\\s]|\\\\*[^/])+\",\"name\":\"entity.name.type.instance.jsdoc\"}]},{\"begin\":\"((@)(?:arg|argument|const|constant|member|namespace|param|prop|property|var))\\\\s+(?=\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"}},\"end\":\"(?=\\\\s|\\\\*/|[^]$A-\\\\[_a-{}])\",\"patterns\":[{\"include\":\"#jsdoctype\"},{\"match\":\"([$A-Z_a-z][]$.\\\\[\\\\w]*)\",\"name\":\"variable.other.jsdoc\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.optional-value.begin.bracket.square.jsdoc\"},\"2\":{\"name\":\"keyword.operator.assignment.jsdoc\"},\"3\":{\"name\":\"source.embedded.imba\"},\"4\":{\"name\":\"punctuation.definition.optional-value.end.bracket.square.jsdoc\"},\"5\":{\"name\":\"invalid.illegal.syntax.jsdoc\"}},\"match\":\"(\\\\[)\\\\s*[$\\\\w]+(?:(?:\\\\[])?\\\\.[$\\\\w]+)*(?:\\\\s*(=)\\\\s*((?>\\\"(?:\\\\*(?!/)|\\\\\\\\(?!\\\")|[^*\\\\\\\\])*?\\\"|'(?:\\\\*(?!/)|\\\\\\\\(?!')|[^*\\\\\\\\])*?'|\\\\[(?:\\\\*(?!/)|[^*])*?]|(?:\\\\*(?!/)|\\\\s(?!\\\\s*])|\\\\[.*?(?:]|(?=\\\\*/))|[^]*\\\\[\\\\s])*)*))?\\\\s*(?:(])((?:[^*\\\\s]|\\\\*[^/\\\\s])+)?|(?=\\\\*/))\",\"name\":\"variable.other.jsdoc\"}]},{\"begin\":\"((@)(?:define|enum|exception|export|extends|lends|implements|modifies|namespace|private|protected|returns?|suppress|this|throws|type|yields?))\\\\s+(?=\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"}},\"end\":\"(?=\\\\s|\\\\*/|[^]$A-\\\\[_a-{}])\",\"patterns\":[{\"include\":\"#jsdoctype\"}]},{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"entity.name.type.instance.jsdoc\"}},\"match\":\"((@)(?:alias|augments|callback|constructs|emits|event|fires|exports?|extends|external|function|func|host|lends|listens|interface|memberof!?|method|module|mixes|mixin|name|requires|see|this|typedef|uses))\\\\s+((?:[^*@{}\\\\s]|\\\\*[^/])+)\"},{\"begin\":\"((@)(?:default(?:value)?|license|version))\\\\s+(([\\\"']))\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"variable.other.jsdoc\"},\"4\":{\"name\":\"punctuation.definition.string.begin.jsdoc\"}},\"contentName\":\"variable.other.jsdoc\",\"end\":\"(\\\\3)|(?=$|\\\\*/)\",\"endCaptures\":{\"0\":{\"name\":\"variable.other.jsdoc\"},\"1\":{\"name\":\"punctuation.definition.string.end.jsdoc\"}}},{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"},\"3\":{\"name\":\"variable.other.jsdoc\"}},\"match\":\"((@)(?:default(?:value)?|license|tutorial|variation|version))\\\\s+([^*\\\\s]+)\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"}},\"match\":\"(@)(?:abstract|access|alias|api|arg|argument|async|attribute|augments|author|beta|borrows|bubbles|callback|chainable|class|classdesc|code|config|const|constant|constructor|constructs|copyright|default|defaultvalue|define|deprecated|desc|description|dict|emits|enum|event|example|exception|exports?|extends|extension(?:_?for)?|external|externs|file|fileoverview|final|fires|for|func|function|generator|global|hideconstructor|host|ignore|implements|implicitCast|inherit[Dd]oc|inner|instance|interface|internal|kind|lends|license|listens|main|member|memberof!?|method|mixes|mixins?|modifies|module|name|namespace|noalias|nocollapse|nocompile|nosideeffects|override|overview|package|param|polymer(?:Behavior)?|preserve|private|prop|property|protected|public|read[Oo]nly|record|require[ds]|returns?|see|since|static|struct|submodule|summary|suppress|template|this|throws|todo|tutorial|type|typedef|unrestricted|uses|var|variation|version|virtual|writeOnce|yields?)\\\\b\",\"name\":\"storage.type.class.jsdoc\"},{\"include\":\"#inline-tags\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.block.tag.jsdoc\"}},\"match\":\"((@)[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?)(?=\\\\s+)\"}]},\"expr\":{\"patterns\":[{\"include\":\"#style-declaration\"},{\"include\":\"#object-keys\"},{\"include\":\"#generics-literal\"},{\"include\":\"#tag-literal\"},{\"include\":\"#regex\"},{\"include\":\"#keywords\"},{\"include\":\"#comment\"},{\"include\":\"#literal\"},{\"include\":\"#plain-identifiers\"},{\"include\":\"#plain-accessors\"},{\"include\":\"#pairs\"}]},\"expression\":{\"patterns\":[{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"meta.brace.round.imba\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"meta.brace.round.imba\"}},\"patterns\":[{\"include\":\"#expr\"}]},{\"include\":\"#tag-literal\"},{\"include\":\"#expressionWithoutIdentifiers\"},{\"include\":\"#identifiers\"},{\"include\":\"#expressionPunctuations\"}]},\"expressionPunctuations\":{\"patterns\":[{\"include\":\"#punctuation-comma\"},{\"include\":\"#punctuation-accessor\"}]},\"expressionWithoutIdentifiers\":{\"patterns\":[{\"include\":\"#string\"},{\"include\":\"#regex\"},{\"include\":\"#comment\"},{\"include\":\"#function-expression\"},{\"include\":\"#class-expression\"},{\"include\":\"#ternary-expression\"},{\"include\":\"#new-expr\"},{\"include\":\"#instanceof-expr\"},{\"include\":\"#object-literal\"},{\"include\":\"#expression-operators\"},{\"include\":\"#literal\"},{\"include\":\"#support-objects\"}]},\"generics-literal\":{\"begin\":\"(?<=[])\\\\w])<\",\"beginCaptures\":{\"1\":{\"name\":\"meta.generics.annotation.open.imba\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"meta.generics.annotation.close.imba\"}},\"name\":\"meta.generics.annotation.imba\",\"patterns\":[{\"include\":\"#type-brackets\"}]},\"global-literal\":{\"match\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(global)\\\\b(?!\\\\$)\",\"name\":\"variable.language.global.imba\"},\"identifiers\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.accessor.imba\"},\"2\":{\"name\":\"punctuation.accessor.optional.imba\"},\"3\":{\"name\":\"entity.name.function.property.imba\"}},\"match\":\"(?:(?:(\\\\.)|(\\\\.\\\\.(?!\\\\s*\\\\d|\\\\s+)))\\\\s*)?([$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?)(?=\\\\s*=\\\\{\\\\{functionOrArrowLookup}})\"},{\"captures\":{\"1\":{\"name\":\"punctuation.accessor.imba\"},\"2\":{\"name\":\"punctuation.accessor.optional.imba\"},\"3\":{\"name\":\"variable.other.constant.property.imba\"}},\"match\":\"(?:(\\\\.)|(\\\\.\\\\.(?!\\\\s*\\\\d|\\\\s+)))\\\\s*(#?\\\\p{upper}[$_\\\\d[:upper:]]*)(?![$_[:alnum:]])\"},{\"captures\":{\"1\":{\"name\":\"punctuation.accessor.imba\"},\"2\":{\"name\":\"punctuation.accessor.optional.imba\"},\"3\":{\"name\":\"variable.other.class.property.imba\"}},\"match\":\"(?:(\\\\.)|(\\\\.\\\\.(?!\\\\s*\\\\d|\\\\s+)))(\\\\p{upper}[$_[:alnum:]]*(?:-[$_[:alnum:]]+)*!?)\"},{\"captures\":{\"1\":{\"name\":\"punctuation.accessor.imba\"},\"2\":{\"name\":\"punctuation.accessor.optional.imba\"},\"3\":{\"name\":\"variable.other.property.imba\"}},\"match\":\"(?:(\\\\.)|(\\\\.\\\\.(?!\\\\s*\\\\d|\\\\s+)))(#?[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?)\"},{\"match\":\"(for own|for|if|unless|when)\\\\b\",\"name\":\"keyword.other\"},{\"match\":\"require\",\"name\":\"support.function.require\"},{\"include\":\"#plain-identifiers\"},{\"include\":\"#type-literal\"},{\"include\":\"#generics-literal\"}]},\"inline-css-selector\":{\"begin\":\"^(\\\\t+)(?![-!$%.@^\\\\w]+\\\\s*[:=])\",\"end\":\"(\\\\s*(?=[-!$%.@^\\\\w]+\\\\s*[:=]|[])])|\\\\s*$)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.separator.sel-properties.css\"}},\"name\":\"meta.selector.css\",\"patterns\":[{\"include\":\"#css-selector-innards\"}]},\"inline-styles\":{\"patterns\":[{\"include\":\"#style-property\"},{\"include\":\"#css-property-values\"},{\"include\":\"#style-expr\"}]},\"inline-tags\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.bracket.square.begin.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.bracket.square.end.jsdoc\"}},\"match\":\"(\\\\[)[^]]+(])(?=\\\\{@(?:link|linkcode|linkplain|tutorial))\",\"name\":\"constant.other.description.jsdoc\"},{\"begin\":\"(\\\\{)((@)(?:link(?:code|plain)?|tutorial))\\\\s*\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.bracket.curly.begin.jsdoc\"},\"2\":{\"name\":\"storage.type.class.jsdoc\"},\"3\":{\"name\":\"punctuation.definition.inline.tag.jsdoc\"}},\"end\":\"}|(?=\\\\*/)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.bracket.curly.end.jsdoc\"}},\"name\":\"entity.name.type.instance.jsdoc\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"variable.other.link.underline.jsdoc\"},\"2\":{\"name\":\"punctuation.separator.pipe.jsdoc\"}},\"match\":\"\\\\G((?=https?://)(?:[^*|}\\\\s]|\\\\*/)+)(\\\\|)?\"},{\"captures\":{\"1\":{\"name\":\"variable.other.description.jsdoc\"},\"2\":{\"name\":\"punctuation.separator.pipe.jsdoc\"}},\"match\":\"\\\\G((?:[^*@{|}\\\\s]|\\\\*[^/])+)(\\\\|)?\"}]}]},\"invalid-indentation\":{\"patterns\":[{\"match\":\"^ +\",\"name\":\"invalid.whitespace\"},{\"match\":\"^\\\\t+\\\\s+\",\"name\":\"invalid.whitespace\"}]},\"jsdoctype\":{\"patterns\":[{\"match\":\"\\\\G\\\\{(?:[^*}]|\\\\*[^/}])+$\",\"name\":\"invalid.illegal.type.jsdoc\"},{\"begin\":\"\\\\G(\\\\{)\",\"beginCaptures\":{\"0\":{\"name\":\"entity.name.type.instance.jsdoc\"},\"1\":{\"name\":\"punctuation.definition.bracket.curly.begin.jsdoc\"}},\"contentName\":\"entity.name.type.instance.jsdoc\",\"end\":\"((}))\\\\s*|(?=\\\\*/)\",\"endCaptures\":{\"1\":{\"name\":\"entity.name.type.instance.jsdoc\"},\"2\":{\"name\":\"punctuation.definition.bracket.curly.end.jsdoc\"}},\"patterns\":[{\"include\":\"#brackets\"}]}]},\"keywords\":{\"patterns\":[{\"match\":\"(if|elif|else|unless|switch|when|then|do|import|export|for own|for|while|until|return|yield|try|catch|await|rescue|finally|throw|as|continue|break|extend|augment)(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"keyword.control.imba\"},{\"match\":\"(?<=export)\\\\s+(default)(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"keyword.control.imba\"},{\"match\":\"(?<=import)\\\\s+(type)(?=\\\\s+[$_{\\\\w])\",\"name\":\"keyword.control.imba\"},{\"match\":\"(extend|global|abstract)\\\\s+(?=class|tag|abstract|mixin|interface)\",\"name\":\"keyword.control.imba\"},{\"match\":\"(?<=[$*}\\\\w])\\\\s+(from)(?=\\\\s+[\\\"'])\",\"name\":\"keyword.control.imba\"},{\"match\":\"(def|get|set)(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"storage.type.function.imba\"},{\"match\":\"(pr(?:otected|ivate))\\\\s+(?=def|get|set)\",\"name\":\"keyword.control.imba\"},{\"match\":\"(tag|class|struct|mixin|interface)(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"storage.type.class.imba\"},{\"match\":\"(let|const|constructor)(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"storage.type.imba\"},{\"match\":\"(prop|attr)(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"storage.type.imba\"},{\"match\":\"(static)\\\\s+\",\"name\":\"storage.modifier.imba\"},{\"match\":\"(declare)\\\\s+\",\"name\":\"storage.modifier.imba\"},{\"include\":\"#ops\"},{\"match\":\"((?:|\\\\|\\\\||\\\\?\\\\?|&&|[-%*+^])=)\",\"name\":\"keyword.operator.assignment.imba\"},{\"match\":\"(>=?|<=?)\",\"name\":\"keyword.operator.imba\"},{\"match\":\"(of|delete|!?isa|typeof|!?in|new|!?is|isnt)(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"keyword.operator.imba\"}]},\"literal\":{\"patterns\":[{\"include\":\"#number-with-unit-literal\"},{\"include\":\"#numeric-literal\"},{\"include\":\"#boolean-literal\"},{\"include\":\"#null-literal\"},{\"include\":\"#undefined-literal\"},{\"include\":\"#numericConstant-literal\"},{\"include\":\"#this-literal\"},{\"include\":\"#global-literal\"},{\"include\":\"#super-literal\"},{\"include\":\"#type-literal\"},{\"include\":\"#generics-literal\"},{\"include\":\"#string\"}]},\"mixin-css-selector\":{\"begin\":\"(%[-\\\\w]+)\",\"beginCaptures\":{\"1\":{\"name\":\"entity.other.attribute-name.mixin.css\"}},\"end\":\"(\\\\s*(?=[-!$%.@^\\\\w]+\\\\s*[:=][^:])|\\\\s*$|(?=\\\\s+#\\\\s))\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.separator.sel-properties.css\"}},\"name\":\"meta.selector.css\",\"patterns\":[{\"include\":\"#css-selector-innards\"}]},\"mixin-css-selector-after\":{\"begin\":\"(?<=%[-\\\\w]+)(?![-!$%.@^\\\\w]+\\\\s*[:=][^:])\",\"end\":\"(\\\\s*(?=[-!$%.@^\\\\w]+\\\\s*[:=][^:])|\\\\s*$|(?=\\\\s+#\\\\s))\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.separator.sel-properties.css\"}},\"name\":\"meta.selector.css\",\"patterns\":[{\"include\":\"#css-selector-innards\"}]},\"mixin-declaration\":{\"begin\":\"^(\\\\t*)(%[-\\\\w]+)\",\"beginCaptures\":{\"2\":{\"name\":\"entity.other.attribute-name.mixin.css\"}},\"end\":\"^(?!(\\\\1\\\\t|\\\\s*$))\",\"name\":\"meta.style.imba\",\"patterns\":[{\"include\":\"#mixin-css-selector-after\"},{\"include\":\"#css-comment\"},{\"include\":\"#nested-css-selector\"},{\"include\":\"#inline-styles\"}]},\"nested-css-selector\":{\"begin\":\"^(\\\\t+)(?![-!$%.@^\\\\w]+\\\\s*[:=][^:])\",\"end\":\"(\\\\s*(?=[-!$%.@^\\\\w]+\\\\s*[:=][^:])|\\\\s*$|(?=\\\\s+#\\\\s))\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.separator.sel-properties.css\"}},\"name\":\"meta.selector.css\",\"patterns\":[{\"include\":\"#css-selector-innards\"}]},\"nested-style-declaration\":{\"begin\":\"^(\\\\t+)(?=[\\\\n^]*&)\",\"end\":\"^(?!(\\\\1\\\\t|\\\\s*$))\",\"name\":\"meta.style.imba\",\"patterns\":[{\"include\":\"#nested-css-selector\"},{\"include\":\"#inline-styles\"}]},\"null-literal\":{\"match\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))null(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"constant.language.null.imba\"},\"number-with-unit-literal\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"constant.numeric.imba\"},\"2\":{\"name\":\"keyword.other.unit.imba\"}},\"match\":\"([0-9]+)([a-z]+|%)\"},{\"captures\":{\"1\":{\"name\":\"constant.numeric.decimal.imba\"},\"2\":{\"name\":\"keyword.other.unit.imba\"}},\"match\":\"([0-9]*\\\\.[0-9]+(?:[Ee][-+]?[0-9]+)?)([a-z]+|%)\"}]},\"numeric-literal\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"storage.type.numeric.bigint.imba\"}},\"match\":\"\\\\b(?<!\\\\$)0[Xx]\\\\h[_\\\\h]*(n)?\\\\b(?!\\\\$)\",\"name\":\"constant.numeric.hex.imba\"},{\"captures\":{\"1\":{\"name\":\"storage.type.numeric.bigint.imba\"}},\"match\":\"\\\\b(?<!\\\\$)0[Bb][01][01_]*(n)?\\\\b(?!\\\\$)\",\"name\":\"constant.numeric.binary.imba\"},{\"captures\":{\"1\":{\"name\":\"storage.type.numeric.bigint.imba\"}},\"match\":\"\\\\b(?<!\\\\$)0[Oo]?[0-7][0-7_]*(n)?\\\\b(?!\\\\$)\",\"name\":\"constant.numeric.octal.imba\"},{\"captures\":{\"0\":{\"name\":\"constant.numeric.decimal.imba\"},\"1\":{\"name\":\"meta.delimiter.decimal.period.imba\"},\"2\":{\"name\":\"storage.type.numeric.bigint.imba\"},\"3\":{\"name\":\"meta.delimiter.decimal.period.imba\"},\"4\":{\"name\":\"storage.type.numeric.bigint.imba\"},\"5\":{\"name\":\"meta.delimiter.decimal.period.imba\"},\"6\":{\"name\":\"storage.type.numeric.bigint.imba\"},\"7\":{\"name\":\"storage.type.numeric.bigint.imba\"},\"8\":{\"name\":\"meta.delimiter.decimal.period.imba\"},\"9\":{\"name\":\"storage.type.numeric.bigint.imba\"},\"10\":{\"name\":\"meta.delimiter.decimal.period.imba\"},\"11\":{\"name\":\"storage.type.numeric.bigint.imba\"},\"12\":{\"name\":\"meta.delimiter.decimal.period.imba\"},\"13\":{\"name\":\"storage.type.numeric.bigint.imba\"},\"14\":{\"name\":\"storage.type.numeric.bigint.imba\"}},\"match\":\"(?<!\\\\$)(?:\\\\b[0-9][0-9_]*(\\\\.)[0-9][0-9_]*[Ee][-+]?[0-9][0-9_]*(n)?\\\\b|\\\\b[0-9][0-9_]*(\\\\.)[Ee][-+]?[0-9][0-9_]*(n)?\\\\b|\\\\B(\\\\.)[0-9][0-9_]*[Ee][-+]?[0-9][0-9_]*(n)?\\\\b|\\\\b[0-9][0-9_]*[Ee][-+]?[0-9][0-9_]*(n)?\\\\b|\\\\b[0-9][0-9_]*(\\\\.)[0-9][0-9_]*(n)?\\\\b|\\\\b[0-9][0-9_]*(\\\\.)(n)?\\\\B|\\\\B(\\\\.)[0-9][0-9_]*(n)?\\\\b|\\\\b[0-9][0-9_]*(n)?\\\\b)(?!\\\\$)\"}]},\"numericConstant-literal\":{\"patterns\":[{\"match\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))NaN(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"constant.language.nan.imba\"},{\"match\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))Infinity(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"constant.language.infinity.imba\"}]},\"object-keys\":{\"patterns\":[{\"match\":\"[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?:\",\"name\":\"meta.object-literal.key\"}]},\"ops\":{\"patterns\":[{\"match\":\"\\\\.\\\\.\\\\.\",\"name\":\"keyword.operator.spread.imba\"},{\"match\":\"\\\\*=|(?<!\\\\()/=|%=|\\\\+=|-=|\\\\?=|\\\\?\\\\?=|=\\\\?\",\"name\":\"keyword.operator.assignment.compound.imba\"},{\"match\":\"\\\\^=\\\\?|\\\\|=\\\\?|~=\\\\?|&=|\\\\^=|<<=|>>=|>>>=|\\\\|=\",\"name\":\"keyword.operator.assignment.compound.bitwise.imba\"},{\"match\":\"<<|>>>?\",\"name\":\"keyword.operator.bitwise.shift.imba\"},{\"match\":\"(?:==|!=|[!=~])=\",\"name\":\"keyword.operator.comparison.imba\"},{\"match\":\"<=|>=|<>|[<>]\",\"name\":\"keyword.operator.relational.imba\"},{\"captures\":{\"1\":{\"name\":\"keyword.operator.logical.imba\"},\"2\":{\"name\":\"keyword.operator.arithmetic.imba\"}},\"match\":\"(!)\\\\s*(/)(?![*/])\"},{\"match\":\"!|&&|\\\\|\\\\||\\\\?\\\\?|or\\\\b(?=\\\\s|$)|and\\\\b(?=\\\\s|$)|@\\\\b(?=\\\\s|$)\",\"name\":\"keyword.operator.logical.imba\"},{\"match\":\"\\\\?(?=\\\\s|$)\",\"name\":\"keyword.operator.bitwise.imba\"},{\"match\":\"[\\\\&^|~]\",\"name\":\"keyword.operator.ternary.imba\"},{\"match\":\"=\",\"name\":\"keyword.operator.assignment.imba\"},{\"match\":\"--\",\"name\":\"keyword.operator.decrement.imba\"},{\"match\":\"\\\\+\\\\+\",\"name\":\"keyword.operator.increment.imba\"},{\"match\":\"[-%*+/]\",\"name\":\"keyword.operator.arithmetic.imba\"}]},\"pairs\":{\"patterns\":[{\"include\":\"#curly-braces\"},{\"include\":\"#square-braces\"},{\"include\":\"#round-braces\"}]},\"plain-accessors\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.accessor.imba\"},\"2\":{\"name\":\"variable.other.property.imba\"}},\"match\":\"(\\\\.\\\\.?)([$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?)\"}]},\"plain-identifiers\":{\"patterns\":[{\"match\":\"(\\\\p{upper}[$_\\\\d[:upper:]]*)(?![$_[:alnum:]])\",\"name\":\"variable.other.constant.imba\"},{\"match\":\"\\\\p{upper}[$_[:alnum:]]*(?:-[$_[:alnum:]]+)*!?\",\"name\":\"variable.other.class.imba\"},{\"match\":\"\\\\$\\\\d+\",\"name\":\"variable.special.imba\"},{\"match\":\"\\\\$[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\",\"name\":\"variable.other.internal.imba\"},{\"match\":\"@@+[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\",\"name\":\"variable.other.symbol.imba\"},{\"match\":\"[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\",\"name\":\"variable.other.readwrite.imba\"},{\"match\":\"@[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\",\"name\":\"variable.other.instance.imba\"},{\"match\":\"#+[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\",\"name\":\"variable.other.private.imba\"},{\"match\":\":[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\",\"name\":\"string.symbol.imba\"}]},\"punctuation-accessor\":{\"captures\":{\"1\":{\"name\":\"punctuation.accessor.imba\"},\"2\":{\"name\":\"punctuation.accessor.optional.imba\"}},\"match\":\"(\\\\.)|(\\\\.\\\\.(?!\\\\s*\\\\d|\\\\s+))\"},\"punctuation-comma\":{\"match\":\",\",\"name\":\"punctuation.separator.comma.imba\"},\"punctuation-semicolon\":{\"match\":\";\",\"name\":\"punctuation.terminator.statement.imba\"},\"qstring-double\":{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.imba\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.imba\"}},\"name\":\"string.quoted.double.imba\",\"patterns\":[{\"include\":\"#template-substitution-element\"},{\"include\":\"#string-character-escape\"}]},\"qstring-single\":{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.imba\"}},\"end\":\"(')|([^\\\\n\\\\\\\\])$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.string.end.imba\"},\"2\":{\"name\":\"invalid.illegal.newline.imba\"}},\"name\":\"string.quoted.single.imba\",\"patterns\":[{\"include\":\"#string-character-escape\"}]},\"qstring-single-multi\":{\"begin\":\"'''\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.imba\"}},\"end\":\"'''\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.imba\"}},\"name\":\"string.quoted.single.imba\",\"patterns\":[{\"include\":\"#string-character-escape\"}]},\"regex\":{\"patterns\":[{\"begin\":\"(?<!\\\\+\\\\+|--|})(?<=[!(+,:=?\\\\[]|^return|[^$._[:alnum:]]return|^case|[^$._[:alnum:]]case|=>|&&|\\\\|\\\\||\\\\*/)\\\\s*(/)(?![*/])(?=(?:[^()/\\\\[\\\\\\\\]|\\\\\\\\.|\\\\[([^]\\\\\\\\]|\\\\\\\\.)+]|\\\\(([^)\\\\\\\\]|\\\\\\\\.)+\\\\))+/([gimsuy]+|(?![*/])|(?=/\\\\*))(?!\\\\s*[$0-9A-Z_a-z]))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.string.begin.imba\"}},\"end\":\"(/)([gimsuy]*)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.string.end.imba\"},\"2\":{\"name\":\"keyword.other.imba\"}},\"name\":\"string.regexp.imba\",\"patterns\":[{\"include\":\"#regexp\"}]},{\"begin\":\"((?<![]$)_[:alnum:]]|\\\\+\\\\+|--|}|\\\\*/)|((?<=^return|[^$._[:alnum:]]return|^case|[^$._[:alnum:]]case))\\\\s*)/(?![*/])(?=(?:[^/\\\\[\\\\\\\\]|\\\\\\\\.|\\\\[([^]\\\\\\\\]|\\\\\\\\.)+])+/([gimsuy]+|(?![*/])|(?=/\\\\*))(?!\\\\s*[$0-9A-Z_a-z]))\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.imba\"}},\"end\":\"(/)([gimsuy]*)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.string.end.imba\"},\"2\":{\"name\":\"keyword.other.imba\"}},\"name\":\"string.regexp.imba\",\"patterns\":[{\"include\":\"#regexp\"}]}]},\"regex-character-class\":{\"patterns\":[{\"match\":\"\\\\\\\\[DSWdfnrstvw]|\\\\.\",\"name\":\"constant.other.character-class.regexp\"},{\"match\":\"\\\\\\\\([0-7]{3}|x\\\\h{2}|u\\\\h{4})\",\"name\":\"constant.character.numeric.regexp\"},{\"match\":\"\\\\\\\\c[A-Z]\",\"name\":\"constant.character.control.regexp\"},{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.backslash.regexp\"}]},\"regexp\":{\"patterns\":[{\"match\":\"\\\\\\\\[Bb]|[$^]\",\"name\":\"keyword.control.anchor.regexp\"},{\"captures\":{\"0\":{\"name\":\"keyword.other.back-reference.regexp\"},\"1\":{\"name\":\"variable.other.regexp\"}},\"match\":\"\\\\\\\\(?:[1-9]\\\\d*|k<([$A-Z_a-z][$\\\\w]*)>)\"},{\"match\":\"[*+?]|\\\\{(\\\\d+,\\\\d+|\\\\d+,|,\\\\d+|\\\\d+)}\\\\??\",\"name\":\"keyword.operator.quantifier.regexp\"},{\"match\":\"\\\\|\",\"name\":\"keyword.operator.or.regexp\"},{\"begin\":\"(\\\\()((\\\\?=)|(\\\\?!)|(\\\\?<=)|(\\\\?<!))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.group.regexp\"},\"2\":{\"name\":\"punctuation.definition.group.assertion.regexp\"},\"3\":{\"name\":\"meta.assertion.look-ahead.regexp\"},\"4\":{\"name\":\"meta.assertion.negative-look-ahead.regexp\"},\"5\":{\"name\":\"meta.assertion.look-behind.regexp\"},\"6\":{\"name\":\"meta.assertion.negative-look-behind.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.group.regexp\"}},\"name\":\"meta.group.assertion.regexp\",\"patterns\":[{\"include\":\"#regexp\"}]},{\"begin\":\"\\\\((?:(\\\\?:)|\\\\?<([$A-Z_a-z][$\\\\w]*)>)?\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.group.regexp\"},\"1\":{\"name\":\"punctuation.definition.group.no-capture.regexp\"},\"2\":{\"name\":\"variable.other.regexp\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.group.regexp\"}},\"name\":\"meta.group.regexp\",\"patterns\":[{\"include\":\"#regexp\"}]},{\"begin\":\"(\\\\[)(\\\\^)?\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.character-class.regexp\"},\"2\":{\"name\":\"keyword.operator.negation.regexp\"}},\"end\":\"(])\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.character-class.regexp\"}},\"name\":\"constant.other.character-class.set.regexp\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"constant.character.numeric.regexp\"},\"2\":{\"name\":\"constant.character.control.regexp\"},\"3\":{\"name\":\"constant.character.escape.backslash.regexp\"},\"4\":{\"name\":\"constant.character.numeric.regexp\"},\"5\":{\"name\":\"constant.character.control.regexp\"},\"6\":{\"name\":\"constant.character.escape.backslash.regexp\"}},\"match\":\"(?:.|(\\\\\\\\(?:[0-7]{3}|x\\\\h{2}|u\\\\h{4}))|(\\\\\\\\c[A-Z])|(\\\\\\\\.))-(?:[^]\\\\\\\\]|(\\\\\\\\(?:[0-7]{3}|x\\\\h{2}|u\\\\h{4}))|(\\\\\\\\c[A-Z])|(\\\\\\\\.))\",\"name\":\"constant.other.character-class.range.regexp\"},{\"include\":\"#regex-character-class\"}]},{\"include\":\"#regex-character-class\"}]},\"root\":{\"patterns\":[{\"include\":\"#block\"}]},\"round-braces\":{\"begin\":\"\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"meta.brace.round.imba\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"meta.brace.round.imba\"}},\"patterns\":[{\"include\":\"#expr\"},{\"include\":\"#punctuation-comma\"}]},\"single-line-comment-consuming-line-ending\":{\"begin\":\"(^[\\\\t ]+)?((//|#\\\\s)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.imba\"},\"2\":{\"name\":\"comment.line.double-slash.imba\"},\"3\":{\"name\":\"punctuation.definition.comment.imba\"},\"4\":{\"name\":\"storage.type.internaldeclaration.imba\"},\"5\":{\"name\":\"punctuation.decorator.internaldeclaration.imba\"}},\"contentName\":\"comment.line.double-slash.imba\",\"end\":\"(?=^)\"},\"square-braces\":{\"begin\":\"\\\\s*(\\\\[)\",\"beginCaptures\":{\"1\":{\"name\":\"meta.brace.square.imba\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"meta.brace.square.imba\"}},\"patterns\":[{\"include\":\"#expr\"},{\"include\":\"#punctuation-comma\"}]},\"string\":{\"patterns\":[{\"include\":\"#qstring-single-multi\"},{\"include\":\"#qstring-double-multi\"},{\"include\":\"#qstring-single\"},{\"include\":\"#qstring-double\"},{\"include\":\"#template\"}]},\"string-character-escape\":{\"match\":\"\\\\\\\\(x\\\\h{2}|u\\\\h{4}|u\\\\{\\\\h+}|[012][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)\",\"name\":\"constant.character.escape.imba\"},\"style-declaration\":{\"begin\":\"^(\\\\t*)(?:(global|local|export)\\\\s+)?(?:(scoped)\\\\s+)?(css)\\\\s\",\"beginCaptures\":{\"2\":{\"name\":\"keyword.control.export.imba\"},\"3\":{\"name\":\"storage.modifier.imba\"},\"4\":{\"name\":\"storage.type.style.imba\"}},\"end\":\"^(?!(\\\\1\\\\t|\\\\s*$))\",\"name\":\"meta.style.imba\",\"patterns\":[{\"include\":\"#css-selector\"},{\"include\":\"#css-comment\"},{\"include\":\"#nested-css-selector\"},{\"include\":\"#inline-styles\"}]},\"style-expr\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"constant.numeric.integer.decimal.css\"},\"2\":{\"name\":\"keyword.other.unit.css\"}},\"match\":\"\\\\b([0-9][0-9_]*)(\\\\w+|%)?\"},{\"match\":\"--[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\",\"name\":\"support.constant.property-value.var.css\"},{\"match\":\"(x+s|sm-|md-|lg-|sm|md|lg|x+l|hg|x+h)(?![-\\\\w])\",\"name\":\"support.constant.property-value.size.css\"},{\"match\":\"[$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\",\"name\":\"support.constant.property-value.css\"},{\"begin\":\"(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"name\":\"meta.function.css\",\"patterns\":[{\"include\":\"#style-expr\"}]}]},\"style-property\":{\"patterns\":[{\"begin\":\"(?=[-!$%.@^\\\\w]+\\\\s*[:=])\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.calc.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\s*[:=]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.separator.key-value.css\"}},\"name\":\"meta.property-name.css\",\"patterns\":[{\"match\":\"(?:--|\\\\$)[-$\\\\w]+\",\"name\":\"support.type.property-name.variable.css\"},{\"match\":\"@[!<>]?[0-9]+\",\"name\":\"support.type.property-name.modifier.breakpoint.css\"},{\"match\":\"\\\\^?@+[-$\\\\w]+\",\"name\":\"support.type.property-name.modifier.css\"},{\"match\":\"\\\\^?\\\\.+[-$\\\\w]+\",\"name\":\"support.type.property-name.modifier.flag.css\"},{\"match\":\"\\\\^?%+[-$\\\\w]+\",\"name\":\"support.type.property-name.modifier.state.css\"},{\"match\":\"\\\\.\\\\.[-$\\\\w]+|\\\\^+[%.@][-$\\\\w]+\",\"name\":\"support.type.property-name.modifier.up.css\"},{\"match\":\"\\\\.[-$\\\\w]+\",\"name\":\"support.type.property-name.modifier.is.css\"},{\"match\":\"[-$\\\\w]+\",\"name\":\"support.type.property-name.css\"}]}]},\"super-literal\":{\"match\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))super\\\\b(?!\\\\$)\",\"name\":\"variable.language.super.imba\"},\"tag-attr-name\":{\"begin\":\"([$_\\\\w]+(?:-[$_\\\\w]+)*)\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.imba\"}},\"contentName\":\"entity.other.attribute-name.imba\",\"end\":\"(?=[.=>\\\\[\\\\s])\"},\"tag-attr-value\":{\"begin\":\"(=)\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator.tag.assignment\"}},\"contentName\":\"meta.tag.attribute-value.imba\",\"end\":\"(?=[>\\\\s])\",\"patterns\":[{\"include\":\"#expr\"}]},\"tag-classname\":{\"begin\":\"\\\\.\",\"contentName\":\"entity.other.attribute-name.class.css\",\"end\":\"(?=[(.=>\\\\[\\\\s])\",\"patterns\":[{\"include\":\"#tag-interpolated-content\"}]},\"tag-content\":{\"patterns\":[{\"include\":\"#tag-name\"},{\"include\":\"#tag-expr-name\"},{\"include\":\"#tag-interpolated-content\"},{\"include\":\"#tag-interpolated-parens\"},{\"include\":\"#tag-interpolated-brackets\"},{\"include\":\"#tag-event-handler\"},{\"include\":\"#tag-mixin-name\"},{\"include\":\"#tag-classname\"},{\"include\":\"#tag-ref\"},{\"include\":\"#tag-attr-value\"},{\"include\":\"#tag-attr-name\"},{\"include\":\"#comment\"}]},\"tag-event-handler\":{\"begin\":\"(@[$_\\\\w]+(?:-[$_\\\\w]+)*)\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.event-name.imba\"}},\"contentName\":\"entity.other.tag.event\",\"end\":\"(?=[=>\\\\[\\\\s])\",\"patterns\":[{\"include\":\"#tag-interpolated-content\"},{\"include\":\"#tag-interpolated-parens\"},{\"begin\":\"\\\\.\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.tag\"}},\"end\":\"(?=[.=>\\\\[\\\\s]|$)\",\"name\":\"entity.other.event-modifier.imba\",\"patterns\":[{\"include\":\"#tag-interpolated-parens\"},{\"include\":\"#tag-interpolated-content\"}]}]},\"tag-expr-name\":{\"begin\":\"(?<=<)(?=[{\\\\w])\",\"contentName\":\"entity.name.tag.imba\",\"end\":\"(?=[#$%(.>\\\\[\\\\s])\",\"patterns\":[{\"include\":\"#tag-interpolated-content\"}]},\"tag-interpolated-brackets\":{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.tag.imba\"}},\"contentName\":\"meta.embedded.line.imba\",\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.tag.imba\"}},\"name\":\"meta.tag.expression.imba\",\"patterns\":[{\"include\":\"#inline-css-selector\"},{\"include\":\"#inline-styles\"}]},\"tag-interpolated-content\":{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.tag.imba\"}},\"contentName\":\"meta.embedded.line.imba\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.tag.imba\"}},\"name\":\"meta.tag.expression.imba\",\"patterns\":[{\"include\":\"#expression\"}]},\"tag-interpolated-parens\":{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.tag.imba\"}},\"contentName\":\"meta.embedded.line.imba\",\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.tag.imba\"}},\"name\":\"meta.tag.expression.imba\",\"patterns\":[{\"include\":\"#expression\"}]},\"tag-literal\":{\"patterns\":[{\"begin\":\"(<)(?=[#$%(.@\\\\[{~\\\\w])\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.section.tag.open.imba\"}},\"contentName\":\"meta.tag.attributes.imba\",\"end\":\"(>)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.tag.close.imba\"}},\"name\":\"meta.tag.imba\",\"patterns\":[{\"include\":\"#tag-content\"}]}]},\"tag-mixin-name\":{\"match\":\"(%[-\\\\w]+)\",\"name\":\"entity.other.tag-mixin.imba\"},\"tag-name\":{\"patterns\":[{\"match\":\"(?<=<)(self|global|slot)(?=[(.>\\\\[\\\\s])\",\"name\":\"entity.name.tag.special.imba\"}]},\"tag-ref\":{\"match\":\"(\\\\$[-\\\\w]+)\",\"name\":\"entity.other.tag-ref.imba\"},\"template\":{\"patterns\":[{\"begin\":\"(?=(([$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\\\\s*\\\\??\\\\.\\\\s*)*|(\\\\??\\\\.\\\\s*)?)([$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?)(\\\\{\\\\{typeArguments}}\\\\s*)?`)\",\"end\":\"(?=`)\",\"name\":\"string.template.imba\",\"patterns\":[{\"begin\":\"(?=(([$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?\\\\s*\\\\??\\\\.\\\\s*)*|(\\\\??\\\\.\\\\s*)?)([$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?))\",\"end\":\"(?=(\\\\{\\\\{typeArguments}}\\\\s*)?`)\",\"patterns\":[{\"match\":\"([$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?)\",\"name\":\"entity.name.function.tagged-template.imba\"}]}]},{\"begin\":\"([$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?)\\\\s*(?=(\\\\{\\\\{typeArguments}}\\\\s*)`)\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.tagged-template.imba\"}},\"end\":\"(?=`)\",\"name\":\"string.template.imba\",\"patterns\":[{\"include\":\"#type-arguments\"}]},{\"begin\":\"([$_[:alpha:]][$_[:alnum:]]*(?:-[$_[:alnum:]]+)*[!?]?)?(`)\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.tagged-template.imba\"},\"2\":{\"name\":\"punctuation.definition.string.template.begin.imba\"}},\"end\":\"`\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.template.end.imba\"}},\"name\":\"string.template.imba\",\"patterns\":[{\"include\":\"#template-substitution-element\"},{\"include\":\"#string-character-escape\"}]}]},\"template-substitution-element\":{\"begin\":\"(?<!\\\\\\\\)\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.template-expression.begin.imba\"}},\"contentName\":\"meta.embedded.line.imba\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.template-expression.end.imba\"}},\"name\":\"meta.template.expression.imba\",\"patterns\":[{\"include\":\"#expr\"}]},\"this-literal\":{\"match\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(this|self)\\\\b(?!\\\\$)\",\"name\":\"variable.language.this.imba\"},\"type-annotation\":{\"patterns\":[{\"include\":\"#type-literal\"}]},\"type-brackets\":{\"patterns\":[{\"begin\":\"\\\\{\",\"end\":\"}\",\"patterns\":[{\"include\":\"#type-brackets\"}]},{\"begin\":\"\\\\[\",\"end\":\"]\",\"patterns\":[{\"include\":\"#type-brackets\"}]},{\"begin\":\"<\",\"end\":\">\",\"patterns\":[{\"include\":\"#type-brackets\"}]},{\"begin\":\"\\\\(\",\"end\":\"\\\\)\",\"patterns\":[{\"include\":\"#type-brackets\"}]}]},\"type-literal\":{\"begin\":\"(\\\\\\\\)\",\"beginCaptures\":{\"1\":{\"name\":\"meta.type.annotation.open.imba\"}},\"end\":\"(?=[]),.=}\\\\s]|$)\",\"name\":\"meta.type.annotation.imba\",\"patterns\":[{\"include\":\"#type-brackets\"}]},\"undefined-literal\":{\"match\":\"(?<![$_[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))undefined(?![-$?_[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\",\"name\":\"constant.language.undefined.imba\"}},\"scopeName\":\"source.imba\"}"))

export default [
lang
]
