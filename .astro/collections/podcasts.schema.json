{"$ref": "#/definitions/podcasts", "definitions": {"podcasts": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "publishDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "duration": {"type": "string"}, "audioUrl": {"type": "string"}, "coverImage": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "transcript": {"type": "string"}, "featured": {"type": "boolean", "default": false}, "$schema": {"type": "string"}}, "required": ["title", "description", "publishDate", "audioUrl"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}