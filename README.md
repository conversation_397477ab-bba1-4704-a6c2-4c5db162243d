# 播客站 - 现代化播客网站

一个使用 Astro 和 Tailwind CSS 构建的现代化播客网站，支持音频播放、主题切换、响应式设计，并集成 Cloudflare R2 存储服务。

## ✨ 功能特性

- 🎵 **高级音频播放器** - 支持播放/暂停、进度条拖拽、快进/快退
- 📝 **Markdown 内容管理** - 通过 Markdown 文件管理播客信息
- 🌓 **主题切换** - 支持明暗主题切换，保存用户偏好
- 📱 **响应式设计** - 完美适配 PC、移动端、iPad
- ☁️ **Cloudflare R2 集成** - 音频文件云端存储，免费额度充足
- 🔍 **搜索和筛选** - 支持按标题、描述、标签搜索和筛选
- ⚡ **性能优化** - 基于 Astro 的静态站点生成，加载速度极快

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env.example` 到 `.env` 并填入你的 Cloudflare R2 配置：

```bash
cp .env.example .env
```

### 3. 启动开发服务器

```bash
npm run dev
```

访问 `http://localhost:4321` 查看网站。

## 📁 项目结构

```text
/
├── public/
│   ├── audio/              # 本地音频文件（可选）
│   └── images/             # 图片资源
├── src/
│   ├── components/         # 组件
│   │   ├── AudioPlayer.astro
│   │   ├── Navigation.astro
│   │   ├── PodcastCard.astro
│   │   └── ThemeToggle.astro
│   ├── content/           # 内容集合
│   │   ├── config.ts      # 内容配置
│   │   └── podcasts/      # 播客 Markdown 文件
│   ├── layouts/           # 布局组件
│   │   └── Layout.astro
│   ├── pages/             # 页面
│   │   ├── episodes/      # 播客详情页
│   │   ├── about.astro    # 关于页面
│   │   ├── episodes.astro # 播客列表页
│   │   └── index.astro    # 首页
│   ├── styles/            # 样式文件
│   │   └── global.css
│   └── utils/             # 工具函数
│       ├── cloudflare-r2.ts
│       └── content.ts
└── package.json
```

## ☁️ Cloudflare R2 配置

### 免费额度

Cloudflare R2 提供慷慨的免费额度：
- **存储空间**: 10 GB/月
- **Class A 操作**: 100万次/月（上传、列表等）
- **Class B 操作**: 1000万次/月（下载、读取等）
- **出站流量**: 10 GB/月

对于大多数播客网站来说，这个免费额度完全够用！

### 设置步骤

1. **创建 Cloudflare 账户**
   - 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - 注册或登录账户

2. **创建 R2 存储桶**
   ```bash
   # 在 Cloudflare Dashboard 中：
   # 1. 进入 R2 Object Storage
   # 2. 点击 "Create bucket"
   # 3. 输入桶名称（如：podcast-audio）
   # 4. 选择区域（建议选择离用户最近的区域）
   ```

3. **获取 API 凭证**
   ```bash
   # 在 Cloudflare Dashboard 中：
   # 1. 进入 "My Profile" > "API Tokens"
   # 2. 创建 R2 Token
   # 3. 记录 Account ID、Access Key ID、Secret Access Key
   ```

4. **配置环境变量**
   ```bash
   CLOUDFLARE_ACCOUNT_ID=your_account_id
   CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key_id
   CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_access_key
   CLOUDFLARE_R2_BUCKET_NAME=podcast-audio
   ```

5. **（可选）设置自定义域名**
   ```bash
   # 如果你有自己的域名，可以设置自定义域名：
   CLOUDFLARE_R2_PUBLIC_URL=https://audio.yourpodcast.com
   ```

### 上传音频文件

你可以通过以下方式上传音频文件到 R2：

1. **Cloudflare Dashboard**
   - 在 R2 控制台中直接上传文件

2. **命令行工具**
   ```bash
   # 使用 wrangler CLI
   npm install -g wrangler
   wrangler r2 object put podcast-audio/episode-001.mp3 --file ./episode-001.mp3
   ```

3. **编程方式**
   - 使用 AWS SDK 或 Cloudflare API

## 📝 添加新播客

在 `src/content/podcasts/` 目录下创建新的 Markdown 文件：

```markdown
---
title: "第四期：新的话题"
description: "这期我们将讨论..."
publishDate: 2024-02-01
duration: "30:15"
audioUrl: "https://your-r2-bucket.account-id.r2.cloudflarestorage.com/episode-004.mp3"
tags: ["技术", "讨论"]
featured: false
---

# 节目内容

这里是节目的详细内容...
```

## 🎨 自定义主题

网站支持明暗主题切换，你可以在 `src/styles/global.css` 中自定义主题颜色。

## 📱 响应式设计

网站使用 Tailwind CSS 构建，完美适配：
- 📱 移动端（320px+）
- 📱 平板（768px+）
- 💻 桌面端（1024px+）

## 🛠️ 开发命令

| 命令 | 说明 |
| :--- | :--- |
| `npm run dev` | 启动开发服务器 |
| `npm run build` | 构建生产版本 |
| `npm run preview` | 预览构建结果 |
| `npm run astro check` | 类型检查 |

## 🚀 部署

推荐部署到以下平台：
- **Vercel** - 零配置部署
- **Netlify** - 支持表单和函数
- **Cloudflare Pages** - 与 R2 完美集成

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请：
1. 查看文档
2. 搜索已有 Issues
3. 创建新 Issue
