<!DOCTYPE html><html lang="zh-CN" class="scroll-smooth"> <head><meta charset="UTF-8"><meta name="description" content="了解播客站的故事、使命和团队"><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator" content="Astro v5.13.3"><title>关于我们 - 播客站</title><script>
      // 主题切换脚本
      const theme = (() => {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme');
        }
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          return 'dark';
        }
        return 'light';
      })();
      
      if (theme === 'light') {
        document.documentElement.classList.remove('dark');
      } else {
        document.documentElement.classList.add('dark');
      }
      
      window.localStorage.setItem('theme', theme);
    </script><link rel="stylesheet" href="/_astro/about.Dbcwj0bn.css"></head> <body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300">  <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="flex justify-between items-center h-16"> <!-- Logo --> <div class="flex items-center"> <a href="/" class="flex items-center space-x-2"> <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24"> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path> </svg> <span class="text-xl font-bold text-gray-900 dark:text-white">播客站</span> </a> </div> <!-- Desktop Navigation --> <div class="hidden md:block"> <div class="ml-10 flex items-baseline space-x-4"> <a href="/" class="text-gray-900 dark:text-white hover:text-blue-500 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
首页
</a> <a href="/episodes" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
所有节目
</a> <a href="/about" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
关于
</a> </div> </div> <!-- Theme Toggle and Mobile Menu Button --> <div class="flex items-center space-x-4"> <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200" aria-label="切换主题"> <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"> <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path> </svg> <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path> </svg> </button> <script type="module">const d=document.getElementById("theme-toggle"),e=document.getElementById("theme-toggle-dark-icon"),t=document.getElementById("theme-toggle-light-icon");function n(){document.documentElement.classList.contains("dark")?(t?.classList.remove("hidden"),e?.classList.add("hidden")):(t?.classList.add("hidden"),e?.classList.remove("hidden"))}n();d?.addEventListener("click",function(){document.documentElement.classList.contains("dark")?(document.documentElement.classList.remove("dark"),localStorage.setItem("theme","light")):(document.documentElement.classList.add("dark"),localStorage.setItem("theme","dark")),n()});</script> <!-- Mobile menu button --> <div class="md:hidden"> <button id="mobile-menu-button" class="p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500" aria-expanded="false" aria-label="打开主菜单"> <svg id="menu-icon" class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> <svg id="close-icon" class="hidden h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </button> </div> </div> </div> </div> <!-- Mobile menu --> <div id="mobile-menu" class="md:hidden hidden"> <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"> <a href="/" class="text-gray-900 dark:text-white hover:text-blue-500 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
首页
</a> <a href="/episodes" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
所有节目
</a> <a href="/about" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
关于
</a> </div> </div> </nav> <script type="module">const t=document.getElementById("mobile-menu-button"),d=document.getElementById("mobile-menu"),n=document.getElementById("menu-icon"),s=document.getElementById("close-icon");t?.addEventListener("click",()=>{const e=t.getAttribute("aria-expanded")==="true";t.setAttribute("aria-expanded",(!e).toString()),e?(d?.classList.add("hidden"),n?.classList.remove("hidden"),s?.classList.add("hidden")):(d?.classList.remove("hidden"),n?.classList.add("hidden"),s?.classList.remove("hidden"))});const i=d?.querySelectorAll("a");i?.forEach(e=>{e.addEventListener("click",()=>{d?.classList.add("hidden"),n?.classList.remove("hidden"),s?.classList.add("hidden"),t?.setAttribute("aria-expanded","false")})});</script> <main class="min-h-screen bg-white dark:bg-gray-900"> <!-- Hero Section --> <section class="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 py-20"> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center"> <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
关于播客站
</h1> <p class="text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
我们致力于创造一个分享知识、启发思考的播客平台，让每一个声音都能被听见。
</p> </div> </section> <!-- Mission Section --> <section class="py-16"> <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"> <div> <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">
我们的使命
</h2> <p class="text-lg text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
在信息爆炸的时代，我们相信高质量的内容和深度的思考比以往任何时候都更加重要。播客站的使命是为创作者提供一个展示才华的舞台，为听众提供有价值的内容。
</p> <p class="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
我们希望通过播客这种媒介，连接不同背景的人们，促进知识的传播和思想的碰撞，让世界变得更加美好。
</p> </div> <div class="bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg p-8 text-white"> <div class="grid grid-cols-2 gap-6"> <div class="text-center"> <div class="text-3xl font-bold mb-2">100+</div> <div class="text-blue-100">播客节目</div> </div> <div class="text-center"> <div class="text-3xl font-bold mb-2">10K+</div> <div class="text-blue-100">活跃听众</div> </div> <div class="text-center"> <div class="text-3xl font-bold mb-2">50+</div> <div class="text-blue-100">创作者</div> </div> <div class="text-center"> <div class="text-3xl font-bold mb-2">24/7</div> <div class="text-blue-100">在线服务</div> </div> </div> </div> </div> </div> </section> <!-- Values Section --> <section class="py-16 bg-gray-50 dark:bg-gray-800"> <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="text-center mb-12"> <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
我们的价值观
</h2> <p class="text-lg text-gray-600 dark:text-gray-300">
这些核心价值观指导着我们的每一个决定
</p> </div> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> <div class="text-center"> <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4"> <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20"> <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path> </svg> </div> <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
质量至上
</h3> <p class="text-gray-600 dark:text-gray-300">
我们坚持提供高质量的内容，每一期节目都经过精心制作和审核。
</p> </div> <div class="text-center"> <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"> <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20"> <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path> </svg> </div> <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
包容开放
</h3> <p class="text-gray-600 dark:text-gray-300">
我们欢迎不同观点和声音，创造一个包容、开放的交流环境。
</p> </div> <div class="text-center"> <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4"> <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20"> <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path> </svg> </div> <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
持续创新
</h3> <p class="text-gray-600 dark:text-gray-300">
我们不断探索新的技术和形式，为用户提供更好的播客体验。
</p> </div> </div> </div> </section> <!-- Team Section --> <section class="py-16"> <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="text-center mb-12"> <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
我们的团队
</h2> <p class="text-lg text-gray-600 dark:text-gray-300">
一群热爱播客的专业人士
</p> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> <div class="text-center"> <div class="w-32 h-32 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center"> <span class="text-2xl font-bold text-white">张</span> </div> <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
张三
</h3> <p class="text-blue-600 dark:text-blue-400 mb-3">创始人 & CEO</p> <p class="text-gray-600 dark:text-gray-300 text-sm">
拥有10年媒体行业经验，致力于推动播客文化的发展。
</p> </div> <div class="text-center"> <div class="w-32 h-32 bg-gradient-to-br from-green-400 to-green-600 rounded-full mx-auto mb-4 flex items-center justify-center"> <span class="text-2xl font-bold text-white">李</span> </div> <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
李四
</h3> <p class="text-blue-600 dark:text-blue-400 mb-3">技术总监</p> <p class="text-gray-600 dark:text-gray-300 text-sm">
资深全栈开发工程师，专注于音频技术和用户体验优化。
</p> </div> <div class="text-center"> <div class="w-32 h-32 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center"> <span class="text-2xl font-bold text-white">王</span> </div> <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
王五
</h3> <p class="text-blue-600 dark:text-blue-400 mb-3">内容总监</p> <p class="text-gray-600 dark:text-gray-300 text-sm">
前知名媒体编辑，负责内容策划和质量把控。
</p> </div> </div> </div> </section> <!-- Contact Section --> <section class="py-16 bg-blue-600 dark:bg-blue-800"> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center"> <h2 class="text-3xl font-bold text-white mb-6">
联系我们
</h2> <p class="text-xl text-blue-100 mb-8">
有任何问题或建议？我们很乐意听到您的声音
</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto"> <div class="bg-white bg-opacity-10 rounded-lg p-6"> <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4"> <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20"> <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path> <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path> </svg> </div> <h3 class="text-lg font-semibold text-white mb-2">邮箱</h3> <p class="text-blue-100"><EMAIL></p> </div> <div class="bg-white bg-opacity-10 rounded-lg p-6"> <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4"> <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"> <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348z"></path> </svg> </div> <h3 class="text-lg font-semibold text-white mb-2">微信</h3> <p class="text-blue-100">podcast_official</p> </div> </div> </div> </section> </main>  </body></html>