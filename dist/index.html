<!DOCTYPE html><html lang="zh-CN" class="scroll-smooth"> <head><meta charset="UTF-8"><meta name="description" content="一个现代化的播客网站"><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator" content="Astro v5.13.3"><title>播客站 - 发现精彩内容</title><script>
      // 主题切换脚本
      const theme = (() => {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme');
        }
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          return 'dark';
        }
        return 'light';
      })();
      
      if (theme === 'light') {
        document.documentElement.classList.remove('dark');
      } else {
        document.documentElement.classList.add('dark');
      }
      
      window.localStorage.setItem('theme', theme);
    </script><link rel="stylesheet" href="/_astro/about.Dbcwj0bn.css">
<style>.line-clamp-2[data-astro-cid-dx6ndjfb]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.line-clamp-3[data-astro-cid-dx6ndjfb]{display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}
</style></head> <body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300">  <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="flex justify-between items-center h-16"> <!-- Logo --> <div class="flex items-center"> <a href="/" class="flex items-center space-x-2"> <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24"> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path> </svg> <span class="text-xl font-bold text-gray-900 dark:text-white">播客站</span> </a> </div> <!-- Desktop Navigation --> <div class="hidden md:block"> <div class="ml-10 flex items-baseline space-x-4"> <a href="/" class="text-gray-900 dark:text-white hover:text-blue-500 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
首页
</a> <a href="/episodes" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
所有节目
</a> <a href="/about" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
关于
</a> </div> </div> <!-- Theme Toggle and Mobile Menu Button --> <div class="flex items-center space-x-4"> <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200" aria-label="切换主题"> <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"> <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path> </svg> <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path> </svg> </button> <script type="module">const d=document.getElementById("theme-toggle"),e=document.getElementById("theme-toggle-dark-icon"),t=document.getElementById("theme-toggle-light-icon");function n(){document.documentElement.classList.contains("dark")?(t?.classList.remove("hidden"),e?.classList.add("hidden")):(t?.classList.add("hidden"),e?.classList.remove("hidden"))}n();d?.addEventListener("click",function(){document.documentElement.classList.contains("dark")?(document.documentElement.classList.remove("dark"),localStorage.setItem("theme","light")):(document.documentElement.classList.add("dark"),localStorage.setItem("theme","dark")),n()});</script> <!-- Mobile menu button --> <div class="md:hidden"> <button id="mobile-menu-button" class="p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500" aria-expanded="false" aria-label="打开主菜单"> <svg id="menu-icon" class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> <svg id="close-icon" class="hidden h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </button> </div> </div> </div> </div> <!-- Mobile menu --> <div id="mobile-menu" class="md:hidden hidden"> <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"> <a href="/" class="text-gray-900 dark:text-white hover:text-blue-500 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
首页
</a> <a href="/episodes" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
所有节目
</a> <a href="/about" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
关于
</a> </div> </div> </nav> <script type="module">const t=document.getElementById("mobile-menu-button"),d=document.getElementById("mobile-menu"),n=document.getElementById("menu-icon"),s=document.getElementById("close-icon");t?.addEventListener("click",()=>{const e=t.getAttribute("aria-expanded")==="true";t.setAttribute("aria-expanded",(!e).toString()),e?(d?.classList.add("hidden"),n?.classList.remove("hidden"),s?.classList.add("hidden")):(d?.classList.remove("hidden"),n?.classList.add("hidden"),s?.classList.remove("hidden"))});const i=d?.querySelectorAll("a");i?.forEach(e=>{e.addEventListener("click",()=>{d?.classList.add("hidden"),n?.classList.remove("hidden"),s?.classList.add("hidden"),t?.setAttribute("aria-expanded","false")})});</script> <main class="min-h-screen"> <!-- Hero Section --> <section class="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 py-20"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="text-center"> <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
发现精彩
<span class="text-blue-600 dark:text-blue-400">播客内容</span> </h1> <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
探索知识、分享见解、启发思考。在这里，每一期节目都是一次新的发现之旅。
</p> <div class="flex flex-col sm:flex-row gap-4 justify-center"> <a href="/episodes" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200">
浏览所有节目
</a> <a href="#latest" class="bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 border border-blue-600 dark:border-blue-400 hover:bg-blue-50 dark:hover:bg-gray-700 px-8 py-3 rounded-lg font-semibold transition-colors duration-200">
最新节目
</a> </div> </div> </div> </section> <!-- Featured Podcasts --> <section class="py-16 bg-white dark:bg-gray-900"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="text-center mb-12"> <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
精选节目
</h2> <p class="text-gray-600 dark:text-gray-300">
编辑推荐的优质内容
</p> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden" data-astro-cid-dx6ndjfb> <a href="/episodes/episode-001" class="block" data-astro-cid-dx6ndjfb> <!-- 封面图片 --> <div class="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden" data-astro-cid-dx6ndjfb> <img src="/images/episode-001-cover.jpg" alt="第一期：欢迎来到我们的播客" class="w-full h-full object-cover" loading="lazy" data-astro-cid-dx6ndjfb> <!-- 播放按钮覆盖层 --> <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center" data-astro-cid-dx6ndjfb> <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-110" data-astro-cid-dx6ndjfb> <svg class="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M8 5v14l11-7z" data-astro-cid-dx6ndjfb></path> </svg> </div> </div> </div> <!-- 内容区域 --> <div class="p-6" data-astro-cid-dx6ndjfb> <div class="flex items-start justify-between mb-3" data-astro-cid-dx6ndjfb> <h3 class="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 flex-1 mr-2" data-astro-cid-dx6ndjfb> 第一期：欢迎来到我们的播客 </h3> <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full whitespace-nowrap" data-astro-cid-dx6ndjfb> 25:30 </span> </div> <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4" data-astro-cid-dx6ndjfb> 在这一期中，我们将介绍这个播客的主题和目标，以及我们希望与听众分享的内容。 </p> <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400" data-astro-cid-dx6ndjfb> <time datetime="2024-01-15T00:00:00.000Z" data-astro-cid-dx6ndjfb> 2024年1月15日 </time> <div class="flex items-center space-x-1" data-astro-cid-dx6ndjfb> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> <span data-astro-cid-dx6ndjfb>播放</span> </div> </div> </div> </a> </article>  </div> </div> </section> <!-- Latest Podcasts --> <section id="latest" class="py-16 bg-gray-50 dark:bg-gray-800"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="text-center mb-12"> <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
最新节目
</h2> <p class="text-gray-600 dark:text-gray-300">
最新发布的播客内容
</p> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden" data-astro-cid-dx6ndjfb> <a href="/episodes/episode-003" class="block" data-astro-cid-dx6ndjfb> <!-- 封面图片 --> <div class="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden" data-astro-cid-dx6ndjfb> <div class="w-full h-full flex items-center justify-center" data-astro-cid-dx6ndjfb> <svg class="w-16 h-16 text-white opacity-80" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> </div> <!-- 播放按钮覆盖层 --> <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center" data-astro-cid-dx6ndjfb> <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-110" data-astro-cid-dx6ndjfb> <svg class="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M8 5v14l11-7z" data-astro-cid-dx6ndjfb></path> </svg> </div> </div> </div> <!-- 内容区域 --> <div class="p-6" data-astro-cid-dx6ndjfb> <div class="flex items-start justify-between mb-3" data-astro-cid-dx6ndjfb> <h3 class="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 flex-1 mr-2" data-astro-cid-dx6ndjfb> 第三期：创业故事分享 </h3> <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full whitespace-nowrap" data-astro-cid-dx6ndjfb> 28:45 </span> </div> <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4" data-astro-cid-dx6ndjfb> 邀请成功创业者分享他们的创业经历，包括挑战、机遇和经验教训。 </p> <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400" data-astro-cid-dx6ndjfb> <time datetime="2024-01-29T00:00:00.000Z" data-astro-cid-dx6ndjfb> 2024年1月29日 </time> <div class="flex items-center space-x-1" data-astro-cid-dx6ndjfb> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> <span data-astro-cid-dx6ndjfb>播放</span> </div> </div> </div> </a> </article> <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden" data-astro-cid-dx6ndjfb> <a href="/episodes/episode-002" class="block" data-astro-cid-dx6ndjfb> <!-- 封面图片 --> <div class="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden" data-astro-cid-dx6ndjfb> <div class="w-full h-full flex items-center justify-center" data-astro-cid-dx6ndjfb> <svg class="w-16 h-16 text-white opacity-80" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> </div> <!-- 播放按钮覆盖层 --> <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center" data-astro-cid-dx6ndjfb> <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-110" data-astro-cid-dx6ndjfb> <svg class="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M8 5v14l11-7z" data-astro-cid-dx6ndjfb></path> </svg> </div> </div> </div> <!-- 内容区域 --> <div class="p-6" data-astro-cid-dx6ndjfb> <div class="flex items-start justify-between mb-3" data-astro-cid-dx6ndjfb> <h3 class="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 flex-1 mr-2" data-astro-cid-dx6ndjfb> 第二期：技术趋势与未来展望 </h3> <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full whitespace-nowrap" data-astro-cid-dx6ndjfb> 32:15 </span> </div> <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4" data-astro-cid-dx6ndjfb> 深入探讨当前的技术趋势，包括人工智能、区块链和云计算等领域的最新发展。 </p> <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400" data-astro-cid-dx6ndjfb> <time datetime="2024-01-22T00:00:00.000Z" data-astro-cid-dx6ndjfb> 2024年1月22日 </time> <div class="flex items-center space-x-1" data-astro-cid-dx6ndjfb> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> <span data-astro-cid-dx6ndjfb>播放</span> </div> </div> </div> </a> </article> <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden" data-astro-cid-dx6ndjfb> <a href="/episodes/episode-001" class="block" data-astro-cid-dx6ndjfb> <!-- 封面图片 --> <div class="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden" data-astro-cid-dx6ndjfb> <img src="/images/episode-001-cover.jpg" alt="第一期：欢迎来到我们的播客" class="w-full h-full object-cover" loading="lazy" data-astro-cid-dx6ndjfb> <!-- 播放按钮覆盖层 --> <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center" data-astro-cid-dx6ndjfb> <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-110" data-astro-cid-dx6ndjfb> <svg class="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M8 5v14l11-7z" data-astro-cid-dx6ndjfb></path> </svg> </div> </div> </div> <!-- 内容区域 --> <div class="p-6" data-astro-cid-dx6ndjfb> <div class="flex items-start justify-between mb-3" data-astro-cid-dx6ndjfb> <h3 class="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 flex-1 mr-2" data-astro-cid-dx6ndjfb> 第一期：欢迎来到我们的播客 </h3> <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full whitespace-nowrap" data-astro-cid-dx6ndjfb> 25:30 </span> </div> <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4" data-astro-cid-dx6ndjfb> 在这一期中，我们将介绍这个播客的主题和目标，以及我们希望与听众分享的内容。 </p> <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400" data-astro-cid-dx6ndjfb> <time datetime="2024-01-15T00:00:00.000Z" data-astro-cid-dx6ndjfb> 2024年1月15日 </time> <div class="flex items-center space-x-1" data-astro-cid-dx6ndjfb> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> <span data-astro-cid-dx6ndjfb>播放</span> </div> </div> </div> </a> </article>  </div> <div class="text-center mt-12"> <a href="/episodes" class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-semibold">
查看所有节目
<svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path> </svg> </a> </div> </div> </section> <!-- Newsletter Section --> <section class="py-16 bg-blue-600 dark:bg-blue-800"> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center"> <h2 class="text-3xl font-bold text-white mb-4">
订阅更新
</h2> <p class="text-blue-100 mb-8">
第一时间获取最新节目通知和独家内容
</p> <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto"> <input type="email" placeholder="输入您的邮箱地址" class="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-300 focus:outline-none" required> <button type="submit" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
订阅
</button> </form> </div> </section> </main>  <footer class="bg-gray-900 text-white py-12"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="grid grid-cols-1 md:grid-cols-4 gap-8"> <div class="col-span-1 md:col-span-2"> <div class="flex items-center space-x-2 mb-4"> <svg class="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 24 24"> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path> </svg> <span class="text-xl font-bold">播客站</span> </div> <p class="text-gray-400 mb-4">
一个专注于分享知识和见解的播客平台，致力于为听众带来有价值的内容。
</p> </div> <div> <h3 class="text-lg font-semibold mb-4">快速链接</h3> <ul class="space-y-2"> <li><a href="/" class="text-gray-400 hover:text-white transition-colors duration-200">首页</a></li> <li><a href="/episodes" class="text-gray-400 hover:text-white transition-colors duration-200">所有节目</a></li> <li><a href="/about" class="text-gray-400 hover:text-white transition-colors duration-200">关于我们</a></li> </ul> </div> <div> <h3 class="text-lg font-semibold mb-4">联系我们</h3> <ul class="space-y-2"> <li class="text-gray-400">邮箱：<EMAIL></li> <li class="text-gray-400">微信：podcast_official</li> </ul> </div> </div> <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400"> <p>&copy; 2024 播客站. 保留所有权利.</p> </div> </div> </footer>  </body></html>