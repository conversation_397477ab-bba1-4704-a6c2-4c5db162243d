<!DOCTYPE html><html lang="zh-CN" class="scroll-smooth"> <head><meta charset="UTF-8"><meta name="description" content="浏览我们的所有播客节目"><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator" content="Astro v5.13.3"><title>所有节目 - 播客站</title><script>
      // 主题切换脚本
      const theme = (() => {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme');
        }
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          return 'dark';
        }
        return 'light';
      })();
      
      if (theme === 'light') {
        document.documentElement.classList.remove('dark');
      } else {
        document.documentElement.classList.add('dark');
      }
      
      window.localStorage.setItem('theme', theme);
    </script><link rel="stylesheet" href="/_astro/about.Dbcwj0bn.css">
<style>.line-clamp-2[data-astro-cid-dx6ndjfb]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.line-clamp-3[data-astro-cid-dx6ndjfb]{display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}
</style></head> <body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300">  <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="flex justify-between items-center h-16"> <!-- Logo --> <div class="flex items-center"> <a href="/" class="flex items-center space-x-2"> <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24"> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path> </svg> <span class="text-xl font-bold text-gray-900 dark:text-white">播客站</span> </a> </div> <!-- Desktop Navigation --> <div class="hidden md:block"> <div class="ml-10 flex items-baseline space-x-4"> <a href="/" class="text-gray-900 dark:text-white hover:text-blue-500 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
首页
</a> <a href="/episodes" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
所有节目
</a> <a href="/about" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
关于
</a> </div> </div> <!-- Theme Toggle and Mobile Menu Button --> <div class="flex items-center space-x-4"> <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200" aria-label="切换主题"> <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"> <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path> </svg> <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path> </svg> </button> <script type="module">const d=document.getElementById("theme-toggle"),e=document.getElementById("theme-toggle-dark-icon"),t=document.getElementById("theme-toggle-light-icon");function n(){document.documentElement.classList.contains("dark")?(t?.classList.remove("hidden"),e?.classList.add("hidden")):(t?.classList.add("hidden"),e?.classList.remove("hidden"))}n();d?.addEventListener("click",function(){document.documentElement.classList.contains("dark")?(document.documentElement.classList.remove("dark"),localStorage.setItem("theme","light")):(document.documentElement.classList.add("dark"),localStorage.setItem("theme","dark")),n()});</script> <!-- Mobile menu button --> <div class="md:hidden"> <button id="mobile-menu-button" class="p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500" aria-expanded="false" aria-label="打开主菜单"> <svg id="menu-icon" class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> <svg id="close-icon" class="hidden h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </button> </div> </div> </div> </div> <!-- Mobile menu --> <div id="mobile-menu" class="md:hidden hidden"> <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"> <a href="/" class="text-gray-900 dark:text-white hover:text-blue-500 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
首页
</a> <a href="/episodes" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
所有节目
</a> <a href="/about" class="text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
关于
</a> </div> </div> </nav> <script type="module">const t=document.getElementById("mobile-menu-button"),d=document.getElementById("mobile-menu"),n=document.getElementById("menu-icon"),s=document.getElementById("close-icon");t?.addEventListener("click",()=>{const e=t.getAttribute("aria-expanded")==="true";t.setAttribute("aria-expanded",(!e).toString()),e?(d?.classList.add("hidden"),n?.classList.remove("hidden"),s?.classList.add("hidden")):(d?.classList.remove("hidden"),n?.classList.add("hidden"),s?.classList.remove("hidden"))});const i=d?.querySelectorAll("a");i?.forEach(e=>{e.addEventListener("click",()=>{d?.classList.add("hidden"),n?.classList.remove("hidden"),s?.classList.add("hidden"),t?.setAttribute("aria-expanded","false")})});</script> <main class="min-h-screen bg-white dark:bg-gray-900"> <!-- Header --> <section class="bg-gradient-to-r from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 py-16"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="text-center"> <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
所有节目
</h1> <p class="text-xl text-gray-600 dark:text-gray-300 mb-8">
探索我们的完整播客库，找到您感兴趣的内容
</p> <!-- Search Bar --> <div class="max-w-md mx-auto"> <div class="relative"> <input type="text" id="search-input" placeholder="搜索节目..." class="w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white dark:border-gray-600"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path> </svg> </div> </div> </div> </div> </div> </section> <!-- Filters and Content --> <section class="py-12"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Tags Filter --> <div class="mb-8"> <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
按标签筛选
</h2> <div class="flex flex-wrap gap-2"> <button class="tag-filter active px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-blue-500 text-white" data-tag="all">
全部
</button> <button class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600" data-tag="AI"> AI </button><button class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600" data-tag="云计算"> 云计算 </button><button class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600" data-tag="介绍"> 介绍 </button><button class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600" data-tag="创业"> 创业 </button><button class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600" data-tag="区块链"> 区块链 </button><button class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600" data-tag="商业"> 商业 </button><button class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600" data-tag="开始"> 开始 </button><button class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600" data-tag="技术"> 技术 </button><button class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600" data-tag="经验分享"> 经验分享 </button> </div> </div> <!-- Results Info --> <div class="mb-6"> <p id="results-info" class="text-gray-600 dark:text-gray-400">
共 3 期节目
</p> </div> <!-- Podcasts Grid --> <div id="podcasts-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> <div class="podcast-item" data-title="第三期：创业故事分享" data-description="邀请成功创业者分享他们的创业经历，包括挑战、机遇和经验教训。" data-tags="创业,商业,经验分享"> <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden" data-astro-cid-dx6ndjfb> <a href="/episodes/episode-003" class="block" data-astro-cid-dx6ndjfb> <!-- 封面图片 --> <div class="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden" data-astro-cid-dx6ndjfb> <div class="w-full h-full flex items-center justify-center" data-astro-cid-dx6ndjfb> <svg class="w-16 h-16 text-white opacity-80" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> </div> <!-- 播放按钮覆盖层 --> <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center" data-astro-cid-dx6ndjfb> <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-110" data-astro-cid-dx6ndjfb> <svg class="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M8 5v14l11-7z" data-astro-cid-dx6ndjfb></path> </svg> </div> </div> </div> <!-- 内容区域 --> <div class="p-6" data-astro-cid-dx6ndjfb> <div class="flex items-start justify-between mb-3" data-astro-cid-dx6ndjfb> <h3 class="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 flex-1 mr-2" data-astro-cid-dx6ndjfb> 第三期：创业故事分享 </h3> <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full whitespace-nowrap" data-astro-cid-dx6ndjfb> 28:45 </span> </div> <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4" data-astro-cid-dx6ndjfb> 邀请成功创业者分享他们的创业经历，包括挑战、机遇和经验教训。 </p> <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400" data-astro-cid-dx6ndjfb> <time datetime="2024-01-29T00:00:00.000Z" data-astro-cid-dx6ndjfb> 2024年1月29日 </time> <div class="flex items-center space-x-1" data-astro-cid-dx6ndjfb> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> <span data-astro-cid-dx6ndjfb>播放</span> </div> </div> </div> </a> </article>  </div><div class="podcast-item" data-title="第二期：技术趋势与未来展望" data-description="深入探讨当前的技术趋势，包括人工智能、区块链和云计算等领域的最新发展。" data-tags="技术,ai,区块链,云计算"> <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden" data-astro-cid-dx6ndjfb> <a href="/episodes/episode-002" class="block" data-astro-cid-dx6ndjfb> <!-- 封面图片 --> <div class="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden" data-astro-cid-dx6ndjfb> <div class="w-full h-full flex items-center justify-center" data-astro-cid-dx6ndjfb> <svg class="w-16 h-16 text-white opacity-80" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> </div> <!-- 播放按钮覆盖层 --> <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center" data-astro-cid-dx6ndjfb> <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-110" data-astro-cid-dx6ndjfb> <svg class="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M8 5v14l11-7z" data-astro-cid-dx6ndjfb></path> </svg> </div> </div> </div> <!-- 内容区域 --> <div class="p-6" data-astro-cid-dx6ndjfb> <div class="flex items-start justify-between mb-3" data-astro-cid-dx6ndjfb> <h3 class="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 flex-1 mr-2" data-astro-cid-dx6ndjfb> 第二期：技术趋势与未来展望 </h3> <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full whitespace-nowrap" data-astro-cid-dx6ndjfb> 32:15 </span> </div> <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4" data-astro-cid-dx6ndjfb> 深入探讨当前的技术趋势，包括人工智能、区块链和云计算等领域的最新发展。 </p> <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400" data-astro-cid-dx6ndjfb> <time datetime="2024-01-22T00:00:00.000Z" data-astro-cid-dx6ndjfb> 2024年1月22日 </time> <div class="flex items-center space-x-1" data-astro-cid-dx6ndjfb> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> <span data-astro-cid-dx6ndjfb>播放</span> </div> </div> </div> </a> </article>  </div><div class="podcast-item" data-title="第一期：欢迎来到我们的播客" data-description="在这一期中，我们将介绍这个播客的主题和目标，以及我们希望与听众分享的内容。" data-tags="介绍,开始"> <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden" data-astro-cid-dx6ndjfb> <a href="/episodes/episode-001" class="block" data-astro-cid-dx6ndjfb> <!-- 封面图片 --> <div class="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden" data-astro-cid-dx6ndjfb> <img src="/images/episode-001-cover.jpg" alt="第一期：欢迎来到我们的播客" class="w-full h-full object-cover" loading="lazy" data-astro-cid-dx6ndjfb> <!-- 播放按钮覆盖层 --> <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center" data-astro-cid-dx6ndjfb> <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-110" data-astro-cid-dx6ndjfb> <svg class="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M8 5v14l11-7z" data-astro-cid-dx6ndjfb></path> </svg> </div> </div> </div> <!-- 内容区域 --> <div class="p-6" data-astro-cid-dx6ndjfb> <div class="flex items-start justify-between mb-3" data-astro-cid-dx6ndjfb> <h3 class="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 flex-1 mr-2" data-astro-cid-dx6ndjfb> 第一期：欢迎来到我们的播客 </h3> <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full whitespace-nowrap" data-astro-cid-dx6ndjfb> 25:30 </span> </div> <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4" data-astro-cid-dx6ndjfb> 在这一期中，我们将介绍这个播客的主题和目标，以及我们希望与听众分享的内容。 </p> <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400" data-astro-cid-dx6ndjfb> <time datetime="2024-01-15T00:00:00.000Z" data-astro-cid-dx6ndjfb> 2024年1月15日 </time> <div class="flex items-center space-x-1" data-astro-cid-dx6ndjfb> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" data-astro-cid-dx6ndjfb> <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" data-astro-cid-dx6ndjfb></path> </svg> <span data-astro-cid-dx6ndjfb>播放</span> </div> </div> </div> </a> </article>  </div> </div> <!-- No Results Message --> <div id="no-results" class="hidden text-center py-12"> <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2.306z"></path> </svg> <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
没有找到匹配的节目
</h3> <p class="text-gray-600 dark:text-gray-400">
尝试调整搜索条件或选择不同的标签
</p> </div> </div> </section> </main>  </body></html> <script type="module">class l{constructor(){this.searchInput=document.getElementById("search-input"),this.tagFilters=document.querySelectorAll(".tag-filter"),this.podcastItems=document.querySelectorAll(".podcast-item"),this.resultsInfo=document.getElementById("results-info"),this.noResults=document.getElementById("no-results"),this.currentTag="all",this.currentSearch="",this.init()}init(){this.searchInput?.addEventListener("input",e=>{this.currentSearch=e.target.value.toLowerCase(),this.filterPodcasts()}),this.tagFilters.forEach(e=>{e.addEventListener("click",a=>{const t=a.target,i=t.dataset.tag||"all";this.tagFilters.forEach(s=>{s.classList.remove("active","bg-blue-500","text-white"),s.classList.add("bg-gray-200","dark:bg-gray-700","text-gray-700","dark:text-gray-300")}),t.classList.remove("bg-gray-200","dark:bg-gray-700","text-gray-700","dark:text-gray-300"),t.classList.add("active","bg-blue-500","text-white"),this.currentTag=i,this.filterPodcasts()})})}filterPodcasts(){let e=0;this.podcastItems.forEach(a=>{const t=a,i=t.dataset.title||"",s=t.dataset.description||"",r=t.dataset.tags||"",c=!this.currentSearch||i.includes(this.currentSearch)||s.includes(this.currentSearch),n=this.currentTag==="all"||r.includes(this.currentTag.toLowerCase());c&&n?(t.style.display="block",e++):t.style.display="none"}),this.resultsInfo&&(this.currentSearch||this.currentTag!=="all"?this.resultsInfo.textContent=`找到 ${e} 期节目`:this.resultsInfo.textContent=`共 ${this.podcastItems.length} 期节目`),this.noResults&&(e===0?this.noResults.classList.remove("hidden"):this.noResults.classList.add("hidden"))}}document.addEventListener("DOMContentLoaded",()=>{new l});</script>