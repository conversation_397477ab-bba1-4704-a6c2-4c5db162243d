---
export interface Props {
  src: string;
  title: string;
  artist?: string;
}

const { src, title, artist } = Astro.props;
---

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
  <div class="flex items-center justify-between mb-4">
    <div class="flex-1 min-w-0">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
        {title}
      </h3>
      {artist && (
        <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
          {artist}
        </p>
      )}
    </div>
  </div>

  <audio id="audio-player" preload="metadata" class="hidden">
    <source src={src} type="audio/mpeg" />
    您的浏览器不支持音频播放。
  </audio>

  <!-- 进度条 -->
  <div class="mb-4">
    <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
      <span id="current-time">0:00</span>
      <span id="duration">0:00</span>
    </div>
    <div class="relative">
      <div class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer" id="progress-container">
        <div class="h-2 bg-blue-500 rounded-full transition-all duration-150" id="progress-bar" style="width: 0%"></div>
      </div>
      <div class="absolute top-0 w-4 h-4 bg-blue-500 rounded-full shadow-lg transform -translate-y-1 -translate-x-2 opacity-0 transition-opacity duration-150" id="progress-thumb" style="left: 0%"></div>
    </div>
  </div>

  <!-- 控制按钮 -->
  <div class="flex items-center justify-center space-x-4">
    <button
      id="rewind-btn"
      class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
      aria-label="后退15秒"
    >
      <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M11.99 5V1l-5 5 5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6h-2c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8zm-1.1 11h-.85v-3.26l-1.01.31v-.69l1.77-.63h.09V16zm4.28-1.76c0 .32-.03.6-.1.82s-.17.42-.29.57-.26.26-.42.33-.34.1-.53.1-.37-.03-.52-.08-.28-.14-.39-.23-.19-.21-.25-.36-.09-.3-.09-.48h.85c.02.18.08.32.2.42s.25.15.41.15.3-.05.39-.16.14-.25.14-.43c0-.64-.22-.96-.67-.96-.19 0-.36.04-.51.13l-.01-.02.01-1.93h1.79v.68H14.5l-.01.87c.25-.12.51-.18.77-.18.22 0 .42.05.61.16s.33.25.45.44.18.41.18.67z"/>
      </svg>
    </button>

    <button
      id="play-pause-btn"
      class="p-4 rounded-full bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-200"
      aria-label="播放/暂停"
    >
      <svg id="play-icon" class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M8 5v14l11-7z"/>
      </svg>
      <svg id="pause-icon" class="w-8 h-8 hidden" fill="currentColor" viewBox="0 0 24 24">
        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
      </svg>
    </button>

    <button
      id="forward-btn"
      class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
      aria-label="前进15秒"
    >
      <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 5V1l5 5-5 5V7c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6h2c0 4.42-3.58 8-8 8s-8-3.58-8-8 3.58-8 8-8zm1.1 11h.85v-3.26l1.01.31v-.69l-1.77-.63h-.09V16zm-4.28-1.76c0 .*********.82s.***********.***********.*********.37-.03.52-.08.28-.14.39-.23.19-.21.25-.36.09-.3.09-.48h-.85c-.02.18-.08.32-.2.42s-.25.15-.41.15-.3-.05-.39-.16-.14-.25-.14-.43c0-.64.22-.96.67-.96.19 0 .36.04.51.13l.01-.02-.01-1.93H9.5v.68h1.31l.01.87c-.25-.12-.51-.18-.77-.18-.22 0-.42.05-.61.16s-.33.25-.45.44-.18.41-.18.67z"/>
      </svg>
    </button>
  </div>
</div>

<script>
  class AudioPlayer {
    constructor() {
      this.audio = document.getElementById('audio-player') as HTMLAudioElement;
      this.playPauseBtn = document.getElementById('play-pause-btn');
      this.playIcon = document.getElementById('play-icon');
      this.pauseIcon = document.getElementById('pause-icon');
      this.rewindBtn = document.getElementById('rewind-btn');
      this.forwardBtn = document.getElementById('forward-btn');
      this.progressContainer = document.getElementById('progress-container');
      this.progressBar = document.getElementById('progress-bar');
      this.progressThumb = document.getElementById('progress-thumb');
      this.currentTimeEl = document.getElementById('current-time');
      this.durationEl = document.getElementById('duration');
      
      this.isDragging = false;
      
      this.init();
    }

    init() {
      this.bindEvents();
    }

    bindEvents() {
      // 播放/暂停按钮
      this.playPauseBtn?.addEventListener('click', () => this.togglePlayPause());
      
      // 快进/快退按钮
      this.rewindBtn?.addEventListener('click', () => this.skip(-15));
      this.forwardBtn?.addEventListener('click', () => this.skip(15));
      
      // 音频事件
      this.audio?.addEventListener('loadedmetadata', () => this.updateDuration());
      this.audio?.addEventListener('timeupdate', () => this.updateProgress());
      this.audio?.addEventListener('ended', () => this.onEnded());
      
      // 进度条事件
      this.progressContainer?.addEventListener('click', (e) => this.seek(e));
      this.progressContainer?.addEventListener('mousedown', (e) => this.startDrag(e));
      this.progressContainer?.addEventListener('mousemove', (e) => this.onDrag(e));
      this.progressContainer?.addEventListener('mouseup', () => this.endDrag());
      this.progressContainer?.addEventListener('mouseleave', () => this.endDrag());
      
      // 触摸事件（移动端）
      this.progressContainer?.addEventListener('touchstart', (e) => this.startDrag(e));
      this.progressContainer?.addEventListener('touchmove', (e) => this.onDrag(e));
      this.progressContainer?.addEventListener('touchend', () => this.endDrag());
    }

    togglePlayPause() {
      if (this.audio?.paused) {
        this.audio.play();
        this.playIcon?.classList.add('hidden');
        this.pauseIcon?.classList.remove('hidden');
      } else {
        this.audio?.pause();
        this.playIcon?.classList.remove('hidden');
        this.pauseIcon?.classList.add('hidden');
      }
    }

    skip(seconds: number) {
      if (this.audio) {
        this.audio.currentTime += seconds;
      }
    }

    updateDuration() {
      if (this.audio && this.durationEl) {
        this.durationEl.textContent = this.formatTime(this.audio.duration);
      }
    }

    updateProgress() {
      if (this.audio && !this.isDragging) {
        const progress = (this.audio.currentTime / this.audio.duration) * 100;
        this.setProgress(progress);
        
        if (this.currentTimeEl) {
          this.currentTimeEl.textContent = this.formatTime(this.audio.currentTime);
        }
      }
    }

    setProgress(percentage: number) {
      if (this.progressBar && this.progressThumb) {
        this.progressBar.style.width = `${percentage}%`;
        this.progressThumb.style.left = `${percentage}%`;
      }
    }

    seek(e: MouseEvent | TouchEvent) {
      if (this.audio && this.progressContainer) {
        const rect = this.progressContainer.getBoundingClientRect();
        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
        const percentage = (clientX - rect.left) / rect.width;
        this.audio.currentTime = percentage * this.audio.duration;
      }
    }

    startDrag(e: MouseEvent | TouchEvent) {
      this.isDragging = true;
      this.progressThumb?.classList.remove('opacity-0');
      this.seek(e);
    }

    onDrag(e: MouseEvent | TouchEvent) {
      if (this.isDragging) {
        this.seek(e);
      }
    }

    endDrag() {
      this.isDragging = false;
      this.progressThumb?.classList.add('opacity-0');
    }

    onEnded() {
      this.playIcon?.classList.remove('hidden');
      this.pauseIcon?.classList.add('hidden');
    }

    formatTime(seconds: number): string {
      if (isNaN(seconds)) return '0:00';
      
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  }

  // 初始化播放器
  document.addEventListener('DOMContentLoaded', () => {
    new AudioPlayer();
  });
</script>
