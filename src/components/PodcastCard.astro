---
export interface Props {
  title: string;
  description: string;
  publishDate: string;
  duration?: string;
  slug: string;
  coverImage?: string;
}

const { title, description, publishDate, duration, slug, coverImage } = Astro.props;

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
---

<article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
  <a href={`/episodes/${slug}`} class="block">
    <!-- 封面图片 -->
    <div class="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden">
      {coverImage ? (
        <img 
          src={coverImage} 
          alt={title}
          class="w-full h-full object-cover"
          loading="lazy"
        />
      ) : (
        <div class="w-full h-full flex items-center justify-center">
          <svg class="w-16 h-16 text-white opacity-80" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z"/>
          </svg>
        </div>
      )}
      
      <!-- 播放按钮覆盖层 -->
      <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
        <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-110">
          <svg class="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8 5v14l11-7z"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="p-6">
      <div class="flex items-start justify-between mb-3">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 flex-1 mr-2">
          {title}
        </h3>
        {duration && (
          <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full whitespace-nowrap">
            {duration}
          </span>
        )}
      </div>

      <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4">
        {description}
      </p>

      <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
        <time datetime={publishDate}>
          {formatDate(publishDate)}
        </time>
        
        <div class="flex items-center space-x-1">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z"/>
          </svg>
          <span>播放</span>
        </div>
      </div>
    </div>
  </a>
</article>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
