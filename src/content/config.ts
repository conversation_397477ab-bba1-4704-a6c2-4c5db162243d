import { defineCollection, z } from 'astro:content';

const podcastsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    publishDate: z.date(),
    duration: z.string().optional(),
    audioUrl: z.string(),
    coverImage: z.string().optional(),
    tags: z.array(z.string()).optional(),
    transcript: z.string().optional(),
    featured: z.boolean().default(false),
  }),
});

export const collections = {
  podcasts: podcastsCollection,
};
