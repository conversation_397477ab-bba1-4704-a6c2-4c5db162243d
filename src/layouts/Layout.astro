---
import '../styles/global.css';

export interface Props {
  title: string;
  description?: string;
}

const { title, description = "一个现代化的播客网站" } = Astro.props;
---

<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
    <script is:inline>
      // 主题切换脚本
      const theme = (() => {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme');
        }
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          return 'dark';
        }
        return 'light';
      })();
      
      if (theme === 'light') {
        document.documentElement.classList.remove('dark');
      } else {
        document.documentElement.classList.add('dark');
      }
      
      window.localStorage.setItem('theme', theme);
    </script>
  </head>
  <body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300">
    <slot />
  </body>
</html>
