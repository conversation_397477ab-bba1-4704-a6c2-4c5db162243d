import { getCollection } from 'astro:content';

// 获取所有播客内容
export async function getAllPodcasts() {
  const podcasts = await getCollection('podcasts');
  return podcasts.sort((a, b) => 
    new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime()
  );
}

// 获取特色播客
export async function getFeaturedPodcasts() {
  const podcasts = await getAllPodcasts();
  return podcasts.filter(podcast => podcast.data.featured);
}

// 获取最新播客
export async function getLatestPodcasts(limit: number = 6) {
  const podcasts = await getAllPodcasts();
  return podcasts.slice(0, limit);
}

// 根据标签获取播客
export async function getPodcastsByTag(tag: string) {
  const podcasts = await getAllPodcasts();
  return podcasts.filter(podcast => 
    podcast.data.tags?.includes(tag)
  );
}

// 获取所有标签
export async function getAllTags() {
  const podcasts = await getAllPodcasts();
  const tags = new Set<string>();
  
  podcasts.forEach(podcast => {
    podcast.data.tags?.forEach(tag => tags.add(tag));
  });
  
  return Array.from(tags).sort();
}

// 格式化日期
export function formatDate(date: Date): string {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// 格式化时长
export function formatDuration(duration: string): string {
  // 假设输入格式为 "25:30" 或 "1:25:30"
  const parts = duration.split(':');
  if (parts.length === 2) {
    return `${parts[0]}分${parts[1]}秒`;
  } else if (parts.length === 3) {
    return `${parts[0]}小时${parts[1]}分${parts[2]}秒`;
  }
  return duration;
}

// 生成播客摘要
export function generateExcerpt(content: string, maxLength: number = 150): string {
  // 移除 Markdown 标记
  const plainText = content
    .replace(/#{1,6}\s+/g, '') // 移除标题标记
    .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
    .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，保留文本
    .replace(/`(.*?)`/g, '$1') // 移除代码标记
    .replace(/\n+/g, ' ') // 将换行符替换为空格
    .trim();

  if (plainText.length <= maxLength) {
    return plainText;
  }

  // 在单词边界处截断
  const truncated = plainText.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');
  
  if (lastSpaceIndex > maxLength * 0.8) {
    return truncated.substring(0, lastSpaceIndex) + '...';
  }
  
  return truncated + '...';
}
