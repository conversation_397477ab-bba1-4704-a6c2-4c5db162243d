// Cloudflare R2 配置和工具函数

export interface R2Config {
  accountId: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  publicUrl?: string; // 自定义域名或 R2 公共 URL
}

// 从环境变量获取 R2 配置
export function getR2Config(): R2Config {
  return {
    accountId: import.meta.env.CLOUDFLARE_ACCOUNT_ID || '',
    accessKeyId: import.meta.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: import.meta.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
    bucketName: import.meta.env.CLOUDFLARE_R2_BUCKET_NAME || 'podcast-audio',
    publicUrl: import.meta.env.CLOUDFLARE_R2_PUBLIC_URL || '',
  };
}

// 生成 R2 音频文件 URL
export function getAudioUrl(filename: string): string {
  const config = getR2Config();
  
  if (config.publicUrl) {
    // 使用自定义域名
    return `${config.publicUrl}/${filename}`;
  } else {
    // 使用 R2 默认 URL 格式
    return `https://${config.bucketName}.${config.accountId}.r2.cloudflarestorage.com/${filename}`;
  }
}

// 验证音频文件是否存在（可选功能）
export async function checkAudioExists(filename: string): Promise<boolean> {
  try {
    const url = getAudioUrl(filename);
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('Error checking audio file:', error);
    return false;
  }
}

// 音频文件上传功能（用于管理后台）
export async function uploadAudioToR2(file: File, filename: string): Promise<string> {
  const config = getR2Config();
  
  if (!config.accessKeyId || !config.secretAccessKey) {
    throw new Error('R2 credentials not configured');
  }

  // 这里需要使用 AWS SDK 或类似的库来上传文件到 R2
  // 由于这是前端代码，实际的上传应该通过后端 API 进行
  // 这里只是示例代码结构
  
  const uploadUrl = `https://${config.accountId}.r2.cloudflarestorage.com/${config.bucketName}/${filename}`;
  
  try {
    const response = await fetch('/api/upload-audio', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        filename,
        fileSize: file.size,
        fileType: file.type,
      }),
    });

    if (!response.ok) {
      throw new Error('Upload failed');
    }

    const result = await response.json();
    return result.url;
  } catch (error) {
    console.error('Error uploading audio:', error);
    throw error;
  }
}

// 音频文件元数据
export interface AudioMetadata {
  filename: string;
  size: number;
  duration?: number;
  format: string;
  uploadDate: Date;
}

// 获取音频文件元数据
export async function getAudioMetadata(filename: string): Promise<AudioMetadata | null> {
  try {
    const response = await fetch(`/api/audio-metadata/${filename}`);
    if (!response.ok) {
      return null;
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching audio metadata:', error);
    return null;
  }
}
