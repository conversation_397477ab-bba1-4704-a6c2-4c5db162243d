---
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';
import PodcastCard from '../components/PodcastCard.astro';
import { getLatestPodcasts, getFeaturedPodcasts } from '../utils/content';

const latestPodcasts = await getLatestPodcasts(6);
const featuredPodcasts = await getFeaturedPodcasts();
---

<Layout title="播客站 - 发现精彩内容">
  <Navigation />

  <main class="min-h-screen">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            发现精彩
            <span class="text-blue-600 dark:text-blue-400">播客内容</span>
          </h1>
          <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            探索知识、分享见解、启发思考。在这里，每一期节目都是一次新的发现之旅。
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/episodes"
              class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200"
            >
              浏览所有节目
            </a>
            <a
              href="#latest"
              class="bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 border border-blue-600 dark:border-blue-400 hover:bg-blue-50 dark:hover:bg-gray-700 px-8 py-3 rounded-lg font-semibold transition-colors duration-200"
            >
              最新节目
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Podcasts -->
    {featuredPodcasts.length > 0 && (
      <section class="py-16 bg-white dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              精选节目
            </h2>
            <p class="text-gray-600 dark:text-gray-300">
              编辑推荐的优质内容
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredPodcasts.map((podcast) => (
              <PodcastCard
                title={podcast.data.title}
                description={podcast.data.description}
                publishDate={podcast.data.publishDate.toISOString()}
                duration={podcast.data.duration}
                slug={podcast.slug}
                coverImage={podcast.data.coverImage}
              />
            ))}
          </div>
        </div>
      </section>
    )}

    <!-- Latest Podcasts -->
    <section id="latest" class="py-16 bg-gray-50 dark:bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            最新节目
          </h2>
          <p class="text-gray-600 dark:text-gray-300">
            最新发布的播客内容
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {latestPodcasts.map((podcast) => (
            <PodcastCard
              title={podcast.data.title}
              description={podcast.data.description}
              publishDate={podcast.data.publishDate.toISOString()}
              duration={podcast.data.duration}
              slug={podcast.slug}
              coverImage={podcast.data.coverImage}
            />
          ))}
        </div>

        <div class="text-center mt-12">
          <a
            href="/episodes"
            class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-semibold"
          >
            查看所有节目
            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </a>
        </div>
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-blue-600 dark:bg-blue-800">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-white mb-4">
          订阅更新
        </h2>
        <p class="text-blue-100 mb-8">
          第一时间获取最新节目通知和独家内容
        </p>
        <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
          <input
            type="email"
            placeholder="输入您的邮箱地址"
            class="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-300 focus:outline-none"
            required
          />
          <button
            type="submit"
            class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
          >
            订阅
          </button>
        </form>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-2 mb-4">
            <svg class="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <span class="text-xl font-bold">播客站</span>
          </div>
          <p class="text-gray-400 mb-4">
            一个专注于分享知识和见解的播客平台，致力于为听众带来有价值的内容。
          </p>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-4">快速链接</h3>
          <ul class="space-y-2">
            <li><a href="/" class="text-gray-400 hover:text-white transition-colors duration-200">首页</a></li>
            <li><a href="/episodes" class="text-gray-400 hover:text-white transition-colors duration-200">所有节目</a></li>
            <li><a href="/about" class="text-gray-400 hover:text-white transition-colors duration-200">关于我们</a></li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-4">联系我们</h3>
          <ul class="space-y-2">
            <li class="text-gray-400">邮箱：<EMAIL></li>
            <li class="text-gray-400">微信：podcast_official</li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
        <p>&copy; 2024 播客站. 保留所有权利.</p>
      </div>
    </div>
  </footer>
</Layout>
