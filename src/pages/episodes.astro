---
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';
import PodcastCard from '../components/PodcastCard.astro';
import { getAllPodcasts, getAllTags } from '../utils/content';

const allPodcasts = await getAllPodcasts();
const allTags = await getAllTags();
---

<Layout title="所有节目 - 播客站" description="浏览我们的所有播客节目">
  <Navigation />
  
  <main class="min-h-screen bg-white dark:bg-gray-900">
    <!-- Header -->
    <section class="bg-gradient-to-r from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            所有节目
          </h1>
          <p class="text-xl text-gray-600 dark:text-gray-300 mb-8">
            探索我们的完整播客库，找到您感兴趣的内容
          </p>
          
          <!-- Search Bar -->
          <div class="max-w-md mx-auto">
            <div class="relative">
              <input
                type="text"
                id="search-input"
                placeholder="搜索节目..."
                class="w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white dark:border-gray-600"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Filters and Content -->
    <section class="py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Tags Filter -->
        <div class="mb-8">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            按标签筛选
          </h2>
          <div class="flex flex-wrap gap-2">
            <button
              class="tag-filter active px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-blue-500 text-white"
              data-tag="all"
            >
              全部
            </button>
            {allTags.map((tag) => (
              <button
                class="tag-filter px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
                data-tag={tag}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>

        <!-- Results Info -->
        <div class="mb-6">
          <p id="results-info" class="text-gray-600 dark:text-gray-400">
            共 {allPodcasts.length} 期节目
          </p>
        </div>

        <!-- Podcasts Grid -->
        <div id="podcasts-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {allPodcasts.map((podcast) => (
            <div 
              class="podcast-item" 
              data-title={podcast.data.title.toLowerCase()}
              data-description={podcast.data.description.toLowerCase()}
              data-tags={podcast.data.tags?.join(',').toLowerCase() || ''}
            >
              <PodcastCard
                title={podcast.data.title}
                description={podcast.data.description}
                publishDate={podcast.data.publishDate.toISOString()}
                duration={podcast.data.duration}
                slug={podcast.slug}
                coverImage={podcast.data.coverImage}
              />
            </div>
          ))}
        </div>

        <!-- No Results Message -->
        <div id="no-results" class="hidden text-center py-12">
          <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2.306z" />
          </svg>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            没有找到匹配的节目
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            尝试调整搜索条件或选择不同的标签
          </p>
        </div>
      </div>
    </section>
  </main>
</Layout>

<script>
  // 搜索和筛选功能
  class PodcastFilter {
    constructor() {
      this.searchInput = document.getElementById('search-input') as HTMLInputElement;
      this.tagFilters = document.querySelectorAll('.tag-filter');
      this.podcastItems = document.querySelectorAll('.podcast-item');
      this.resultsInfo = document.getElementById('results-info');
      this.noResults = document.getElementById('no-results');
      
      this.currentTag = 'all';
      this.currentSearch = '';
      
      this.init();
    }

    init() {
      // 搜索输入事件
      this.searchInput?.addEventListener('input', (e) => {
        this.currentSearch = (e.target as HTMLInputElement).value.toLowerCase();
        this.filterPodcasts();
      });

      // 标签筛选事件
      this.tagFilters.forEach(button => {
        button.addEventListener('click', (e) => {
          const target = e.target as HTMLButtonElement;
          const tag = target.dataset.tag || 'all';
          
          // 更新活动状态
          this.tagFilters.forEach(btn => {
            btn.classList.remove('active', 'bg-blue-500', 'text-white');
            btn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
          });
          
          target.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
          target.classList.add('active', 'bg-blue-500', 'text-white');
          
          this.currentTag = tag;
          this.filterPodcasts();
        });
      });
    }

    filterPodcasts() {
      let visibleCount = 0;

      this.podcastItems.forEach(item => {
        const element = item as HTMLElement;
        const title = element.dataset.title || '';
        const description = element.dataset.description || '';
        const tags = element.dataset.tags || '';

        // 检查搜索条件
        const matchesSearch = !this.currentSearch || 
          title.includes(this.currentSearch) || 
          description.includes(this.currentSearch);

        // 检查标签条件
        const matchesTag = this.currentTag === 'all' || 
          tags.includes(this.currentTag.toLowerCase());

        if (matchesSearch && matchesTag) {
          element.style.display = 'block';
          visibleCount++;
        } else {
          element.style.display = 'none';
        }
      });

      // 更新结果信息
      if (this.resultsInfo) {
        if (this.currentSearch || this.currentTag !== 'all') {
          this.resultsInfo.textContent = `找到 ${visibleCount} 期节目`;
        } else {
          this.resultsInfo.textContent = `共 ${this.podcastItems.length} 期节目`;
        }
      }

      // 显示/隐藏无结果消息
      if (this.noResults) {
        if (visibleCount === 0) {
          this.noResults.classList.remove('hidden');
        } else {
          this.noResults.classList.add('hidden');
        }
      }
    }
  }

  // 初始化筛选器
  document.addEventListener('DOMContentLoaded', () => {
    new PodcastFilter();
  });
</script>
