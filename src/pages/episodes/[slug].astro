---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import Navigation from '../../components/Navigation.astro';
import AudioPlayer from '../../components/AudioPlayer.astro';
import { formatDate } from '../../utils/content';

export async function getStaticPaths() {
  const podcasts = await getCollection('podcasts');
  return podcasts.map((podcast) => ({
    params: { slug: podcast.slug },
    props: { podcast },
  }));
}

const { podcast } = Astro.props;
const { Content } = await podcast.render();
---

<Layout title={`${podcast.data.title} - 播客站`} description={podcast.data.description}>
  <Navigation />
  
  <main class="min-h-screen bg-white dark:bg-gray-900">
    <article class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- Header -->
      <header class="mb-8">
        <div class="mb-6">
          <a
            href="/episodes"
            class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 mb-4"
          >
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            返回节目列表
          </a>
        </div>
        
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
          {podcast.data.title}
        </h1>
        
        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-6">
          <time datetime={podcast.data.publishDate.toISOString()}>
            {formatDate(podcast.data.publishDate)}
          </time>
          
          {podcast.data.duration && (
            <span class="flex items-center">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
              </svg>
              {podcast.data.duration}
            </span>
          )}
          
          {podcast.data.tags && podcast.data.tags.length > 0 && (
            <div class="flex flex-wrap gap-2">
              {podcast.data.tags.map((tag) => (
                <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs">
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>
        
        <p class="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
          {podcast.data.description}
        </p>
      </header>

      <!-- Audio Player -->
      <div class="mb-12">
        <AudioPlayer
          src={podcast.data.audioUrl}
          title={podcast.data.title}
        />
      </div>

      <!-- Content -->
      <div class="prose prose-lg dark:prose-invert max-w-none">
        <Content />
      </div>

      <!-- Share Section -->
      <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          分享这期节目
        </h3>
        <div class="flex space-x-4">
          <button
            onclick="shareToWeChat()"
            class="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18z"/>
              <path d="M24 14.966c0-2.746-2.746-4.97-6.13-4.97-3.386 0-6.132 2.224-6.132 4.97 0 2.746 2.746 4.971 6.132 4.971a7.16 7.16 0 0 0 2.191-.351 1.226 1.226 0 0 1 .62-.054l1.362.8a.131.131 0 0 0 .128.054.128.128 0 0 0 .128-.128c0-.054-.018-.1-.036-.145l-.273-1.074a.392.392 0 0 1 .146-.46C22.899 17.637 24 16.467 24 14.966zm-8.95-1.188c.642 0 1.162.529 1.162 1.188 0 .651-.52 1.18-1.162 1.18-.642 0-1.162-.529-1.162-1.18 0-.659.52-1.188 1.162-1.188zm3.775 0c.642 0 1.162.529 1.162 1.188 0 .651-.52 1.18-1.162 1.18-.642 0-1.162-.529-1.162-1.18 0-.659.52-1.188 1.162-1.188z"/>
            </svg>
            <span>微信</span>
          </button>
          
          <button
            onclick="shareToWeibo()"
            class="flex items-center space-x-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9.31 8.17c-2.77-.3-5.21 1.12-5.45 3.18-.24 2.06 1.73 4.07 4.5 4.37 2.77.3 5.21-1.12 5.45-3.18.24-2.06-1.73-4.07-4.5-4.37zm-1.73 5.65c-.61.18-1.28-.11-1.49-.65-.21-.54.07-1.14.68-1.32.61-.18 1.28.11 1.49.65.21.54-.07 1.14-.68 1.32zm1.49-2.05c-.24.07-.5-.04-.57-.25-.07-.21.03-.44.27-.51.24-.07.5.04.57.25.07.21-.03.44-.27.51z"/>
              <path d="M15.8 8.7c-.68-.08-1.32.32-1.4 1-.08.68.32 1.32 1 1.4.68.08 1.32-.32 1.4-1 .08-.68-.32-1.32-1-1.4z"/>
              <path d="M20.9 6.88c-1.95-2.12-5.1-2.57-7.65-1.26.43.16.84.36 1.22.6 2.08-1.02 4.65-.7 6.26.94 1.61 1.64 1.93 4.21.94 6.29-.24-.38-.52-.74-.84-1.06.89-2.55.45-5.7-1.5-7.82z"/>
            </svg>
            <span>微博</span>
          </button>
          
          <button
            onclick="copyLink()"
            class="flex items-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"/>
              <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"/>
            </svg>
            <span>复制链接</span>
          </button>
        </div>
      </div>
    </article>
  </main>
</Layout>

<script>
  function shareToWeChat() {
    // 微信分享逻辑
    alert('请复制链接后在微信中分享');
    copyLink();
  }

  function shareToWeibo() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://service.weibo.com/share/share.php?url=${url}&title=${title}`, '_blank');
  }

  function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(() => {
      alert('链接已复制到剪贴板');
    }).catch(() => {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = window.location.href;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('链接已复制到剪贴板');
    });
  }
</script>
